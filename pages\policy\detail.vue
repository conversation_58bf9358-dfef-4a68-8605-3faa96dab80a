<template>
  <view class="page flex-col">
    <view class="section_1 flex-col" v-if="policyDetail">
      <text class="text_2">{{ policyDetail.title }}</text>
      <view class="group_2 flex-row justify-between">
        <text class="text_3">发布于&nbsp;{{ formatDate(policyDetail.publish_date) }}</text>
        <view class="image-text_1 flex-row justify-between">
          <image
            class="thumbnail_1"
            src="/static/images/date-icon.png"
          />
          <text class="text-group_1">{{ policyDetail.view_count || 0 }}</text>
        </view>
      </view>
      <view class="text_4" @tap="handleContentTap">
        <view v-html="processedContent"></view>
      </view>
      <!-- 底部操作栏 -->
      <view class="action-bar-sticky">
        <button @click="toggleLike" class="action-btn" :class="{ active: isLiked }">
          <image class="action-icon" src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG623fa9bc292ffb1567a77740d5a16d26.png" />
          <text class="action-text">{{ policyDetail.like_count > 0 ? policyDetail.like_count : '点赞' }}</text>
        </button>
        <button @click="toggleCollect" class="action-btn" :class="{ active: isCollected }">
          <image class="action-icon" src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG2068a009371010af035cef611bdbdece.png" />
          <text class="action-text" :style="{ color: isCollected ? 'rgba(255,129,68,1.000000)' : 'rgba(26,32,44,1.000000)' }">{{ policyDetail.collect_count > 0 ? policyDetail.collect_count : '收藏' }}</text>
        </button>
        <button open-type="share" class="action-btn">
          <image class="action-icon" src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG76ea202891cfec7465930d0f40e8b0ee.png" />
          <text class="action-text">{{ policyDetail.forward_count > 0 ? policyDetail.forward_count : '转发'}}</text>
        </button>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-else class="loading">
      <text>加载中...</text>
    </view>
  </view>
</template>

<script>
import api from '@/utils/api.js'

export default {
  data() {
    return {
      policyDetail: null,
      policyId: null,
      isLiked: false,
      isCollected: false,
      guideSteps: [
        {
          title: '准备材料',
          desc: '根据业务类型准备相应的申请材料和证明文件'
        },
        {
          title: '提交申请',
          desc: '通过银行或外汇管理部门指定渠道提交申请'
        },
        {
          title: '审核批准',
          desc: '相关部门进行审核，符合条件的予以批准'
        },
        {
          title: '办理业务',
          desc: '获得批准后按照规定办理相关业务手续'
        }
      ]
    }
  },

  computed: {
    processedContent() {
      if (!this.policyDetail || !this.policyDetail.content) {
        return ''
      }

      // 处理相对路径的链接，转换为绝对路径
      let content = this.policyDetail.content

      // 将相对路径的文件链接转换为完整的下载链接
      content = content.replace(
        /href="\/files\//g,
        'href="http://kjrzymt.com/files/'
      )

      // 为所有链接添加样式和数据属性
      content = content.replace(
        /<a\s+([^>]*?)href="([^"]*?)"([^>]*?)>/g,
        '<a $1href="$2"$3 data-link="$2" style="color: #1E90FF; text-decoration: underline; cursor: pointer;">'
      )

      return content
    },


  },

  onLoad(options) {
    this.policyId = options.id
    if (this.policyId) {
      this.loadPolicyDetail()
    }
  },

  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack()
    },



    async loadPolicyDetail() {
      try {
        const res = await api.getPolicyDetail(this.policyId)
        this.policyDetail = res.data

        // 详情加载成功后，开始检查状态和记录浏览
        this.recordView()
        this.checkStatus()
      } catch (error) {
        console.error('加载政策详情失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
      }
    },

    async checkStatus() {
      const token = uni.getStorageSync('token');
      if (!token) return;

      try {
        const params = {
          item_type: 'policy',
          item_id: parseInt(this.policyId)
        };
        const [likeRes, collectRes] = await Promise.all([
          api.checkInteractionStatus({ ...params, actions: ['like'] }),
          api.checkCollectionStatus(params)
        ]);

        if (likeRes.data.like) {
          this.isLiked = true;
        }
        if (collectRes.data.is_collected) {
          this.isCollected = true;
        }
      } catch (error) {
        console.error('检查状态失败:', error);
      }
    },

    async recordView() {
      const token = uni.getStorageSync('token');
      if (!token) {
        return; // 未登录，不记录
      }
      
      const userInfo = uni.getStorageSync('userInfo');
      if (!userInfo || !userInfo.id) {
        console.error('无法获取用户信息，无法记录浏览');
        return;
      }

      try {
        await api.recordView({
          item_type: 'policy',
          item_id: parseInt(this.policyId)
          // 后端会从token中解析user_id，因此前端无需传递
        });
        if (this.policyDetail) {
          this.policyDetail.view_count++;
        }
      } catch (error) {
        console.error('记录浏览失败:', error);
      }
    },

    async toggleLike() {
      const token = uni.getStorageSync('token');
      if (!token) {
        this.promptLogin('点赞');
        return;
      }

      try {
        const res = await api.toggleInteraction({
          action: 'like',
          item_type: 'policy',
          item_id: parseInt(this.policyId)
        });
        
        this.isLiked = res.data.action === 'added';
        this.policyDetail.like_count = res.data.current_count;
        
        uni.showToast({
          title: res.data.message,
          icon: 'none'
        });
      } catch (error) {
        console.error('点赞操作失败:', error);
      }
    },

    async toggleCollect() {
      const token = uni.getStorageSync('token');
      if (!token) {
        this.promptLogin('收藏');
        return;
      }

      try {
        const res = await api.toggleCollection({
          item_type: 'policy',
          item_id: parseInt(this.policyId)
        });
        
        this.isCollected = res.data.action === 'added';
        this.policyDetail.collect_count = res.data.current_count;
        
        uni.showToast({
          title: res.data.message,
          icon: 'none'
        });
      } catch (error) {
        console.error('收藏操作失败:', error);
      }
    },

    consultPolicy() {
      uni.switchTab({
        url: '/pages/consultation/index'
      })
    },

    promptLogin(action) {
      uni.showModal({
        title: '请先登录',
        content: `登录后才能${action}哦`,
        success: (res) => {
          if (res.confirm) {
            uni.switchTab({
              url: '/pages/profile/index'
            });
          }
        }
      });
    },

    formatDate(dateStr) {
      return new Date(dateStr).toLocaleDateString()
    },

    // 处理内容区域的点击事件
    handleContentTap(event) {
      // 在 H5 环境下，尝试获取点击的元素
      if (typeof window !== 'undefined' && event.target) {
        const target = event.target

        // 检查是否点击了链接
        if (target.tagName === 'A' || target.closest('a')) {
          const link = target.tagName === 'A' ? target : target.closest('a')
          const url = link.getAttribute('href') || link.getAttribute('data-link')

          if (url) {
            event.preventDefault()
            event.stopPropagation()
            this.handleLinkClick(url)
          }
        }
      }
    },

    // 处理富文本中的链接点击
    handleLinkClick(url) {
      console.log('点击链接:', url)

      if (url.startsWith('http://') || url.startsWith('https://')) {
        // 外部链接，复制到剪贴板
        uni.setClipboardData({
          data: url,
          success: () => {
            uni.showModal({
              title: '提示',
              content: '链接已复制到剪贴板，请在浏览器中打开',
              showCancel: false
            })
          }
        })
      } else if (url.startsWith('/pages/')) {
        // 内部页面跳转
        uni.navigateTo({
          url: url,
          fail: (err) => {
            console.error('页面跳转失败:', err)
            uni.showToast({
              title: '页面跳转失败',
              icon: 'none'
            })
          }
        })
      } else {
        // 其他链接，也复制到剪贴板
        uni.setClipboardData({
          data: url,
          success: () => {
            uni.showModal({
              title: '提示',
              content: '链接已复制到剪贴板，请在浏览器中打开',
              showCancel: false
            })
          }
        })
      }
    },

    openAttachment() {
      if (!this.policyDetail.attachment_url) return;
      uni.showLoading({ title: '正在打开附件' });
      uni.downloadFile({
        url: this.policyDetail.attachment_url,
        success: (res) => {
          const filePath = res.tempFilePath;
          uni.openDocument({
            filePath: filePath,
            showMenu: true,
            success: () => {
              uni.hideLoading();
            },
            fail: (err) => {
              uni.hideLoading();
              uni.showToast({
                title: '打开附件失败',
                icon: 'none'
              });
              console.error('打开附件失败', err);
            }
          });
        },
        fail: (err) => {
          uni.hideLoading();
          uni.showToast({
            title: '下载附件失败',
            icon: 'none'
          });
          console.error('下载附件失败', err);
        }
      });
    },

    onShareAppMessage() {
      return {
        title: this.policyDetail.title,
        path: `/pages/policy/detail?id=${this.policyId}`,
        imageUrl: '', // 你可以在这里设置一个默认的分享图片
      };
    },
  }
}
</script>

<style lang="scss" scoped>
/* 通用flex类 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}

.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 750rpx;
  min-height: 100vh;
  overflow: hidden;

  .image_1 {
    width: 750rpx;
    height: 88rpx;
  }

  .section_1 {
    position: relative;
    width: 750rpx;
    min-height: calc(100vh - 88rpx);

    .group_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 750rpx;
      height: 88rpx;
      align-items: center;

      .label_1 {
        width: 48rpx;
        height: 48rpx;
        margin: 20rpx 0 0 24rpx;
        cursor: pointer;
      }

      .text_1 {
        width: 144rpx;
        height: 88rpx;
        overflow-wrap: break-word;
        color: rgba(26, 32, 44, 1);
        font-size: 36rpx;
        font-weight: 600;
        text-align: center;
        line-height: 88rpx;
        margin-left: 232rpx;
      }

      .image_2 {
        width: 168rpx;
        height: 64rpx;
        margin: 12rpx 16rpx 0 118rpx;
        cursor: pointer;
      }
    }

    .text_2 {
      width: 624rpx;
      overflow-wrap: break-word;
      color: rgba(26, 32, 44, 1);
      font-size: 48rpx;
      font-weight: 600;
      text-align: left;
      line-height: 66rpx;
      margin: 32rpx 0 0 30rpx;
    }

    .group_2 {
      width: 338rpx;
      height: 32rpx;
      margin: 24rpx 0 0 30rpx;

      .text_3 {
        width: 184rpx;
        height: 32rpx;
        overflow-wrap: break-word;
        color: rgba(147, 152, 160, 1);
        font-size: 24rpx;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 32rpx;
      }

      .image-text_1 {
        width: 88rpx;
        height: 32rpx;
        align-items: center;

        .thumbnail_1 {
          width: 32rpx;
          height: 32rpx;
        }

        .text-group_1 {
          width: 54rpx;
          height: 32rpx;
          overflow-wrap: break-word;
          color: rgba(144, 146, 149, 1);
          font-size: 24rpx;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 32rpx;
          margin-left: 2rpx;
        }
      }
    }

    .text_4 {
      width: 690rpx;
      overflow-wrap: break-word;
      color: rgba(26, 32, 44, 1);
      font-size: 28rpx;
      font-weight: normal;
      text-align: justify;
      line-height: 64rpx;
      margin: 48rpx 0 94rpx 30rpx;
      padding-bottom: 200rpx; /* 为底部操作区域留出空间 */
    }


  }
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 32rpx;
  color: #666;
}

.action-bar-sticky {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  padding: 10rpx 0;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  transition: color 0.2s ease-in-out, transform 0.1s ease;

  &::after {
    border: none;
  }

  &:active {
    transform: scale(0.9);
  }

  &.active {
    color: #DC143C;
  }

  .action-icon {
    width: 44rpx;
    height: 44rpx;
    margin-bottom: 6rpx;
    transition: all 0.2s cubic-bezier(0.18, 0.89, 0.32, 1.28);
  }

  &:active .action-icon {
    transform: scale(0.9);
  }

  .action-text {
    font-size: 24rpx;
  }
}
</style>