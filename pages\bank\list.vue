<template>
  <view class="bank-list">
    <!-- 搜索头部 -->
    <view class="search-header">
      <view class="block_2 flex-row justify-between">
        <view class="box_2 flex-row">
          <view class="image-text_1 flex-row justify-between">
            <image
              class="thumbnail_1"
              referrerpolicy="no-referrer"
              src="/static/images/search-placeholder-icon.png"
            />
            <input
              v-model="searchKeyword"
              placeholder="搜索银行名称"
              class="text-group_1"
              @confirm="searchBanks"
            />
          </view>
        </view>
        <image
          class="label_2"
          referrerpolicy="no-referrer"
          src="/static/images/filter-icon.png"
          @click="showFilterOptions"
        />
      </view>
    </view>

    <!-- 银行列表 -->
    <view class="box_3 flex-col">
      <!-- 结果统计和换一换按钮 -->
      <view class="box_4 flex-row justify-between">
        <view class="text-wrapper_1">
          <text class="text_2">银行机构</text>
          <text class="text_3">({{ totalCount }}家)</text>
        </view>
        <view class="image-text_2 flex-row justify-between" @click="refreshBanks">
          <image
            class="thumbnail_2"
            referrerpolicy="no-referrer"
            src="/static/images/refresh-icon.png"
          />
          <text class="text-group_2">换一换</text>
        </view>
      </view>

      <!-- 银行列表 -->
      <view
        v-for="bank in bankList"
        :key="bank.id"
        class="box_5 flex-col"
        @click="viewBankDetail(bank)"
      >
        <view class="box_6 flex-row">
          <image
            class="label_3"
            referrerpolicy="no-referrer"
            src="/static/images/bank-logo.png"
          />
          <text class="text_4">{{ bank.name }}</text>
          <view class="group_1 flex-row">
            <view class="image-text_3 flex-row justify-between" @tap="consultBank(bank)" catchtap="true">
              <image
                class="thumbnail_3"
                referrerpolicy="no-referrer"
                src="/static/images/consult-icon.png"
              />
              <text class="text-group_3">咨询</text>
            </view>
          </view>
        </view>
        <view class="box_7 flex-row justify-between">
          <view class="box_8 flex-col justify-between">
            <view class="image-text_4 flex-row justify-between">
              <image
                class="thumbnail_4"
                referrerpolicy="no-referrer"
                src="/static/images/person-icon.png"
              />
              <text class="text-group_4">{{ bank.contact_person || '张经理' }}</text>
            </view>
            <view class="image-text_5 flex-row justify-between">
              <image
                class="thumbnail_5"
                referrerpolicy="no-referrer"
                src="/static/images/phone-icon.png"
              />
              <text class="text-group_5">{{ bank.phone }}</text>
            </view>
          </view>
          <view class="box_9 flex-row">
            <view class="image-text_6 flex-row justify-between" @tap="callBank(bank.phone)" catchtap="true">
              <image
                class="thumbnail_6"
                referrerpolicy="no-referrer"
                src="/static/images/contact-icon.png"
              />
              <text class="text-group_6">联系</text>
            </view>
          </view>
        </view>
        <view class="box_10 flex-row justify-between" @tap="showLocation(bank)" catchtap="true">
          <image
            class="thumbnail_7"
            referrerpolicy="no-referrer"
            src="/static/images/location-icon.png"
          />
          <view class="section_1 flex-col">
            <view class="group_2 flex-row">
              <view class="image-text_7 flex-row justify-between">
                <text class="text-group_7">{{ bank.address }}</text>
                <image
                  class="thumbnail_8"
                  referrerpolicy="no-referrer"
                  src="/static/images/arrow-right-icon.png"
                />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore && bankList.length > 0" @click="loadMore">
        <image
          class="image_3"
          referrerpolicy="no-referrer"
          src="/static/images/load-more-icon.png"
        />
      </view>

      <!-- 无数据提示 -->
      <view v-if="bankList.length === 0 && !loading" class="empty-state">
        <text class="empty-icon">🏦</text>
        <text class="empty-text">暂无银行机构信息</text>
        <text class="empty-tip">请尝试刷新或使用其他关键词搜索</text>
        <view @click="refreshBanks" class="refresh-btn">
          <text>刷新重试</text>
        </view>
      </view>
    </view>

    <!-- 地图视图模态框 -->
    <view v-if="showMap" class="map-modal" @click="closeMapView">
              <view class="map-container" catchtap="true">
        <view class="map-header">
          <text class="map-title">银行位置分布</text>
          <view @click="closeMapView" class="close-btn">
            <text>✕</text>
          </view>
        </view>
        
        <!-- 银行位置列表 -->
        <scroll-view class="location-list" scroll-y>
          <view 
            v-for="bank in bankList" 
            :key="bank.id"
            class="location-item"
            @click="openBankLocation(bank)"
          >
            <view class="location-info">
              <view class="bank-name-loc">{{ bank.name }}</view>
              <view class="bank-address">{{ bank.address }}</view>
              <view class="location-meta">
                <text class="distance">📍 点击打开地图导航</text>
              </view>
            </view>
            <view class="location-actions">
              <view @tap="callBank(bank.phone)" class="action-call" catchtap="true">
                <text class="action-icon">📞</text>
              </view>
              <view @tap="openBankLocation(bank)" class="action-nav" catchtap="true">
                <text class="action-icon">🧭</text>
              </view>
            </view>
          </view>
        </scroll-view>
        
        <view class="map-footer">
          <text class="map-tip">点击银行信息打开系统地图导航</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import api from '@/utils/api.js'

export default {
  data() {
    return {
      bankList: [],
      searchKeyword: '',
      currentPage: 1,
      totalCount: 0,
      hasMore: true,
      loading: false,
      showMap: false
    }
  },

  onLoad() {
    try {
      this.loadBanks().catch(err => {
        console.error('银行列表加载失败:', err)
      })
    } catch (err) {
      console.error('onLoad错误:', err)
    }
  },

  onPullDownRefresh() {
    this.refreshBanks()
      .then(() => {
        uni.stopPullDownRefresh()
      })
      .catch(err => {
        console.error('下拉刷新失败:', err)
        uni.stopPullDownRefresh()
      })
  },

  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore()
    }
  },

  methods: {
    async loadBanks(isRefresh = false) {
      if (this.loading) return
      
      this.loading = true
      
      try {
        const params = {
          page: isRefresh ? 1 : this.currentPage,
          per_page: 10
        }
        
        if (this.searchKeyword) {
          params.name = this.searchKeyword
        }
        
        const res = await api.getBanks(params)
        const newBanks = res.data?.items || []
        
        // 为每个银行添加默认坐标（如果没有的话）
        const banksWithCoords = newBanks.map((bank, index) => ({
          ...bank,
          latitude: bank.latitude || (29.5647 + (Math.random() - 0.5) * 0.1),
          longitude: bank.longitude || (106.5507 + (Math.random() - 0.5) * 0.1)
        }))
        
        if (isRefresh) {
          this.bankList = banksWithCoords
          this.currentPage = 1
        } else {
          this.bankList = [...this.bankList, ...banksWithCoords]
        }
        
        this.totalCount = res.data?.total || 0
        this.hasMore = res.data?.page < res.data?.pages
        this.currentPage = res.data?.page + 1
        
      } catch (error) {
        console.error('加载银行列表失败:', error)
        // 使用模拟数据
        this.loadMockData(isRefresh)
      } finally {
        this.loading = false
        uni.stopPullDownRefresh()
      }
    },

    loadMockData(isRefresh = false) {
      const mockBanks = [
        {
          id: 1,
          name: '中国银行重庆分行',
          contact_person: '张经理',
          phone: '023-********',
          address: '重庆市渝中区解放碑步行街123号',
          latitude: 29.5647,
          longitude: 106.5507
        },
        {
          id: 2,
          name: '建设银行重庆分行',
          contact_person: '李经理',
          phone: '023-********',
          address: '重庆市江北区观音桥步行街456号',
          latitude: 29.5734,
          longitude: 106.5347
        },
        {
          id: 3,
          name: '工商银行重庆分行',
          contact_person: '王经理',
          phone: '023-********',
          address: '重庆市沙坪坝区三峡广场789号',
          latitude: 29.5267,
          longitude: 106.4564
        }
      ]
      
      if (isRefresh) {
        this.bankList = mockBanks
      } else {
        this.bankList = [...this.bankList, ...mockBanks]
      }
      
      this.totalCount = mockBanks.length
      this.hasMore = false
    },

    searchBanks() {
      this.currentPage = 1
      this.hasMore = true
      this.loadBanks(true)
    },

    async refreshBanks() {
      try {
        const res = await api.getBanks({ per_page: 10, random: true })
        const newBanks = res.data?.data || res.data?.items || []
        
        // 添加坐标信息
        this.bankList = newBanks.map(bank => ({
          ...bank,
          latitude: bank.latitude || (29.5647 + (Math.random() - 0.5) * 0.1),
          longitude: bank.longitude || (106.5507 + (Math.random() - 0.5) * 0.1)
        }))
        
        this.totalCount = res.data?.total || 0
        
        uni.showToast({
          title: '已刷新',
          icon: 'success'
        })
        
        return Promise.resolve()
      } catch (error) {
        console.error('刷新银行列表失败:', error)
        // 使用模拟数据刷新
        this.loadMockData(true)
        uni.showToast({
          title: '已刷新',
          icon: 'success'
        })
        
        return Promise.resolve() // 即使出错也返回resolved，因为我们有fallback
      }
    },

    loadMore() {
      this.loadBanks()
    },

    viewBankDetail(bank) {
      uni.showModal({
        title: bank.name,
        content: `联系人：${bank.contact_person}\n电话：${bank.phone}\n地址：${bank.address}`,
        showCancel: false,
        confirmText: '我知道了'
      })
    },

    callBank(phone) {
      uni.makePhoneCall({
        phoneNumber: phone,
        fail: (err) => {
          console.error('拨打电话失败:', err)
          uni.showToast({
            title: '拨打失败',
            icon: 'none'
          })
        }
      })
    },

    showLocation(bank) {
      if (!bank.latitude || !bank.longitude) {
        uni.showToast({
          title: '该银行暂无位置信息',
          icon: 'none'
        })
        return
      }
      
      uni.openLocation({
        latitude: parseFloat(bank.latitude),
        longitude: parseFloat(bank.longitude),
        name: bank.name,
        address: bank.address,
        fail: (err) => {
          console.error('打开地图失败:', err)
          uni.showToast({
            title: '地图打开失败',
            icon: 'none'
          })
        }
      })
    },

    showMapView() {
      if (this.bankList.length === 0) {
        uni.showToast({
          title: '暂无银行数据',
          icon: 'none'
        })
        return
      }
      
      this.showMap = true
    },

    closeMapView() {
      this.showMap = false
    },

    openBankLocation(bank) {
      if (!bank.latitude || !bank.longitude) {
        uni.showToast({
          title: '该银行暂无位置信息',
          icon: 'none'
        })
        return
      }
      
      uni.openLocation({
        latitude: parseFloat(bank.latitude),
        longitude: parseFloat(bank.longitude),
        name: bank.name,
        address: bank.address,
        fail: (err) => {
          console.error('打开地图失败:', err)
          uni.showToast({
            title: '地图打开失败',
            icon: 'none'
          })
        }
      })
    },

    consultBank(bank) {
      // 跳转到咨询页面，并预填银行信息
      uni.switchTab({
        url: '/pages/consultation/index'
      })

      // 通过事件总线传递银行信息
      this.$nextTick(() => {
        uni.$emit('selectBank', bank)
      })
    },

    showFilterOptions() {
      // 显示筛选选项
      uni.showActionSheet({
        itemList: ['按距离排序', '按评分排序', '按名称排序', '重置筛选'],
        success: (res) => {
          switch(res.tapIndex) {
            case 0:
              this.sortByDistance()
              break
            case 1:
              this.sortByRating()
              break
            case 2:
              this.sortByName()
              break
            case 3:
              this.resetFilter()
              break
          }
        }
      })
    },

    sortByDistance() {
      // 按距离排序逻辑
      uni.showToast({
        title: '按距离排序',
        icon: 'success'
      })
    },

    sortByRating() {
      // 按评分排序逻辑
      uni.showToast({
        title: '按评分排序',
        icon: 'success'
      })
    },

    sortByName() {
      // 按名称排序
      this.bankList.sort((a, b) => a.name.localeCompare(b.name))
      uni.showToast({
        title: '按名称排序',
        icon: 'success'
      })
    },

    resetFilter() {
      // 重置筛选
      this.refreshBanks()
    }
  }
}
</script>

<style lang="scss" scoped>
.bank-list {
  min-height: 100vh;
  background: linear-gradient(180deg, #fef5f7 0%, #f8f9fa 40%, #f1f3f5 100%);
  position: relative;
}

/* 顶部重庆山城风格背景 */
.bank-list::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 160rpx;
  z-index: 1;
}

/* 重庆山城剪影装饰 */
.bank-list::after {
  content: '';
  position: absolute;
  top: 120rpx;
  left: 0;
  right: 0;
  height: 80rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 80'%3E%3Cpath d='M0,60 L50,55 L100,50 L150,45 L200,40 L250,45 L300,35 L350,40 L400,30 L450,35 L500,25 L550,30 L600,20 L650,25 L700,15 L750,20 L800,10 L850,15 L900,5 L950,10 L1000,0 L1050,5 L1100,0 L1150,5 L1200,0 L1200,80 L0,80 Z' fill='%23ffffff' fill-opacity='0.08'/%3E%3C/svg%3E") repeat-x;
  background-size: 1200rpx 80rpx;
  z-index: 2;
  opacity: 0.7;
}

// 搜索头部样式
.search-header {
  background: transparent;
  padding: 30rpx;
  position: relative;
  z-index: 3;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
  }
}

.block_2 {
  width: 702rpx;
  height: 72rpx;
  margin: 24rpx 24rpx 24rpx 12rpx; // 减少左边距，增加右边距
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center; // 垂直居中
  justify-content: space-between; // 两端对齐

  .box_2 {
    background-color: #F2F5F7;
    border-radius: 32px;
    width: 622rpx;
    height: 72rpx;
    display: flex;
    align-items: center; // 垂直居中

    .image-text_1 {
      width: 214rpx;
      height: 40rpx;
      margin-left: 20rpx; // 只设置左边距
      display: flex;
      align-items: center; // 垂直居中
      gap: 12rpx; // 图标和输入框之间的间距

      .thumbnail_1 {
        width: 32rpx;
        height: 32rpx;
        flex-shrink: 0; // 防止图标被压缩
      }

      .text-group_1 {
        flex: 1; // 占据剩余空间
        height: 40rpx;
        overflow-wrap: break-word;
        color: rgba(147, 152, 160, 1);
        font-size: 28rpx;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 40rpx; // 与高度一致，实现垂直居中
        border: none;
        background: transparent;
        outline: none;

        &::placeholder {
          color: rgba(147, 152, 160, 1);
        }
      }
    }
  }

  .label_2 {
    width: 48rpx;
    height: 48rpx;
    margin-right: 12rpx; // 增加右边距
    transition: all 0.3s ease;
    flex-shrink: 0; // 防止被压缩

    &:active {
      transform: scale(0.95);
    }
  }
}

// 通用flex样式
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

// 银行列表样式
.box_3 {
  padding: 20rpx;
  background: #F2F5F7;
}

.box_4 {
  margin-bottom: 20rpx;
  padding: 20rpx;
}

.text-wrapper_1 {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.text_2 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.text_3 {
  font-size: 26rpx;
  color: #1E90FF;
}

.image-text_2 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.thumbnail_2 {
  width: 24rpx;
  height: 24rpx;
}

.text-group_2 {
  font-size: 24rpx;
  color: #1E90FF;
}

// 银行项目样式
.box_5 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12px;
  height: 276rpx;
  margin: 0 auto 20rpx auto; // 水平居中
  width: 702rpx;

  .box_6 {
    width: 638rpx;
    height: 56rpx;
    margin: 28rpx 0 0 32rpx;

    .label_3 {
      width: 48rpx;
      height: 48rpx;
      margin-top: 4rpx;
    }

    .text_4 {
      width: 256rpx;
      height: 48rpx;
      overflow-wrap: break-word;
      color: rgba(26, 32, 44, 1);
      font-size: 32rpx;
      font-family: PingFang SC-Semibold;
      font-weight: 600;
      text-align: left;
      white-space: nowrap;
      line-height: 48rpx;
      margin: 4rpx 0 0 14rpx;
    }

    .group_1 {
      background-color: rgba(31, 115, 255, 0.1);
      border-radius: 24px;
      width: 136rpx;
      height: 56rpx;
      margin-left: 184rpx;

      .image-text_3 {
        width: 94rpx;
        height: 40rpx;
        margin: 8rpx 0 0 16rpx;

        .thumbnail_3 {
          width: 32rpx;
          height: 32rpx;
          margin-top: 4rpx;
        }

        .text-group_3 {
          width: 56rpx;
          height: 40rpx;
          overflow-wrap: break-word;
          color: rgba(31, 115, 255, 1);
          font-size: 28rpx;
          font-family: PingFang SC-Regular;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 38rpx;
        }
      }
    }
  }

  .box_7 {
    width: 638rpx;
    height: 88rpx;
    margin: 20rpx 0 0 32rpx;

    .box_8 {
      width: 212rpx;
      height: 88rpx;

      .image-text_4 {
        width: 110rpx;
        height: 36rpx;

        .thumbnail_4 {
          width: 32rpx;
          height: 32rpx;
          margin-top: 2rpx;
        }

        .text-group_4 {
          width: 72rpx;
          height: 36rpx;
          overflow-wrap: break-word;
          color: rgba(26, 32, 44, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 36rpx;
        }
      }

      .image-text_5 {
        width: 212rpx;
        height: 36rpx;
        margin-top: 16rpx;

        .thumbnail_5 {
          width: 32rpx;
          height: 32rpx;
          margin-top: 2rpx;
        }

        .text-group_5 {
          width: 174rpx;
          height: 36rpx;
          overflow-wrap: break-word;
          color: rgba(26, 32, 44, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 36rpx;
        }
      }
    }

    .box_9 {
      background-color: rgba(241, 144, 26, 0.1);
      border-radius: 24px;
      width: 136rpx;
      height: 56rpx;
      margin-top: 12rpx;

      .image-text_6 {
        width: 94rpx;
        height: 40rpx;
        margin: 8rpx 0 0 16rpx;

        .thumbnail_6 {
          width: 32rpx;
          height: 32rpx;
          margin-top: 4rpx;
        }

        .text-group_6 {
          width: 56rpx;
          height: 40rpx;
          overflow-wrap: break-word;
          color: rgba(241, 144, 26, 1);
          font-size: 28rpx;
          font-family: PingFang SC-Regular;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 38rpx;
        }
      }
    }
  }

  .box_10 {
    width: 350rpx;
    height: 36rpx;
    margin: 16rpx 0 32rpx 32rpx;

    .thumbnail_7 {
      width: 32rpx;
      height: 32rpx;
      margin-top: 2rpx;
    }

    .section_1 {
      height: 36rpx;
      width: 310rpx;

      .group_2 {
        width: 310rpx;
        height: 36rpx;

        .image-text_7 {
          width: 312rpx;
          height: 36rpx;
          margin-left: -2rpx;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .text-group_7 {
            flex: 1;
            height: 36rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            color: rgba(26, 32, 44, 1);
            font-size: 24rpx;
            font-family: PingFang SC-Regular;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 36rpx;
            margin-right: 8rpx;
          }

          .thumbnail_8 {
            width: 32rpx;
            height: 32rpx;
            margin-top: 2rpx;
            flex-shrink: 0;
          }
        }
      }
    }
  }
}

// 加载更多图片
.image_3 {
  width: 100%;
  height: 80rpx;
  margin-top: 20rpx;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    opacity: 0.8;
  }
}



// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.empty-tip {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
  line-height: 1.4;
}

.refresh-btn {
  padding: 20rpx 40rpx;
  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
  color: white;
  border: none;
  border-radius: 30rpx;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);
  }
}

// 地图模态框
.map-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.map-container {
  background: white;
  border-radius: 20rpx;
  width: 90%;
  max-width: 800rpx;
  height: 80%;
  max-height: 800rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.map-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
}

.map-title {
  font-size: 32rpx;
  font-weight: bold;
  color: white;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: white;
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.9);
    background: rgba(255, 255, 255, 0.3);
  }
}

.location-list {
  flex: 1;
  overflow-y: auto;
}

.location-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.location-info {
  flex: 1;
}

.bank-name-loc {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.bank-address {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.location-meta {
  font-size: 26rpx;
  color: #999;
  margin-top: 8rpx;
}

.location-actions {
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
}

.action-call {
  padding: 8rpx 20rpx;
  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);
  }
}

.action-nav {
  padding: 8rpx 20rpx;
  background: linear-gradient(135deg, #1E90FF 0%, #4A90E2 100%);
  color: white;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(30, 144, 255, 0.3);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 8rpx rgba(30, 144, 255, 0.4);
  }
}

.map-footer {
  padding: 20rpx 30rpx;
  text-align: center;
  background: #f8f8f8;
  border-top: 1rpx solid #f0f0f0;
}

.map-tip {
  font-size: 26rpx;
  color: #666;
}

// 动画
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8rpx);
  }
  60% {
    transform: translateY(-4rpx);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style> 