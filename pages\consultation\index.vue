<template>
  <view class="consultation-page">
    <!-- 咨询类型选择 -->
    <view class="box_2 flex-col">
      <view class="group_2 flex-row">
        <view class="group_3 flex-col"></view>
        <text class="text_2">咨询类型</text>
      </view>
      <view class="group_4 flex-row justify-between">
        <view
          class="section_1 flex-row"
          :class="{ active: consultationType === 'business_consult' }"
          @click="selectType('business_consult')"
        >
          <view class="image-text_1 flex-col justify-between">
            <image
              class="label_1"
              :src="consultationType === 'business_consult' ? '/static/images/business-consult-normal.png' : '/static/images/business-consult-active.png'"
            />
            <view class="text-group_1 flex-col justify-between">
              <text class="text_3">业务咨询</text>
              <text class="text_4">一般性外汇政策咨询</text>
            </view>
          </view>
        </view>
        <view
          class="section_2 flex-row"
          :class="{ active: consultationType === 'policy_demand' }"
          @click="selectType('policy_demand')"
        >
          <view class="image-text_2 flex-col justify-between">
            <image
              class="label_2"
              :src="consultationType === 'policy_demand' ? '/static/images/policy-demand-active.png' : '/static/images/policy-demand-normal.png'"
            />
            <view class="text-group_2 flex-col justify-between">
              <text class="text_5">政策需求</text>
              <text class="text_6">向银行机构提出政策支持与服务需求</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 银行选择（政策需求时显示） -->
    <view class="box_4 flex-col" v-if="consultationType === 'policy_demand'">
      <view class="bank_block_3 flex-row justify-between">
        <view class="bank_block_4 flex-col"></view>
        <text class="bank_text_7">银行机构</text>
      </view>
      <view class="block_5 flex-row" @click="showBankPicker = true">
        <text class="bank_text_8" v-if="selectedBank">{{ selectedBank.name }}</text>
        <text class="bank_text_8" v-else>请选择银行机构</text>
        <image
          class="thumbnail_1"
          src="/static/images/arrow-right.png"
        />
      </view>
    </view>

    <!-- 咨询表单 -->
    <view class="group_5 flex-col">
      <view class="block_1 flex-row justify-between">
        <view class="box_3 flex-col"></view>
        <text class="text_7">咨询信息</text>
      </view>

      <!-- 联系人姓名 -->
      <view class="text-wrapper_1">
        <text class="text_8">*</text>
        <text class="text_9">联系人姓名</text>
      </view>
      <view class="block_2 flex-col justify-center" :class="{ error: errors.contact_name }">
        <view class="text-wrapper_2 flex-col">
          <input
            class="text_10"
            v-model="formData.contact_name"
            placeholder="请输入您的姓名"
            maxlength="20"
            @blur="validateName"
            @input="validateName"
          />
        </view>
      </view>
      <text class="error-text" v-if="errors.contact_name">{{ errors.contact_name }}</text>

      <!-- 联系电话 -->
      <view class="text-wrapper_3">
        <text class="text_11">*</text>
        <text class="text_12">联系电话</text>
      </view>
      <view class="block_3 flex-col justify-center" :class="{ error: errors.contact_phone }">
        <view class="text-wrapper_4 flex-col">
          <input
            class="text_13"
            v-model="formData.contact_phone"
            placeholder="请输入您的联系电话"
            type="number"
            maxlength="11"
            @blur="validatePhone"
            @input="validatePhone"
          />
        </view>
      </view>
      <text class="error-text" v-if="errors.contact_phone">{{ errors.contact_phone }}</text>

      <!-- 咨询内容 -->
      <view class="text-wrapper_5">
        <text class="text_14">*</text>
        <text class="text_15">咨询内容</text>
      </view>
      <view class="text-wrapper_7 flex-col" :class="{ error: errors.content }">
        <textarea
          class="form-textarea-new"
          v-model="formData.content"
          placeholder="请详细描述您的咨询内容，我们将尽快为您回复"
          maxlength="600"
          @blur="validateContent"
          @input="validateContent"
        ></textarea>
        <text class="text_18">{{ formData.content.length }}/600</text>
      </view>
      <text class="error-text" v-if="errors.content">{{ errors.content }}</text>

      <!-- 提交按钮 -->
      <view class="text-wrapper_6 flex-col" @click="submitConsultation" :class="{ disabled: !canSubmit }">
        <text class="text_16">提交咨询</text>
      </view>
    </view>

    <!-- 温馨提示 -->
    <view class="group_6 flex-col">
      <view class="box_4 flex-row justify-between">
        <image
          class="thumbnail_1"
          src="/static/images/tip-icon.png"
        />
        <text class="text_19">温馨提示</text>
      </view>
      <text class="paragraph_1">
        ·我们将及时回复您的咨询<br />·如需紧急咨询，请直接联系相关银行机构<br />·国家外汇管理局重庆市分局咨询热线：023-********<br />·服务时间：周一至周五（节假日除外）8:30-12:00、14:00-17:30
      </text>
    </view>

    <!-- 银行选择弹窗 -->
    <view class="modal-overlay" v-if="showBankPicker" @click="showBankPicker = false">
                <view class="bank-picker" catchtap="true">
        <view class="picker-header">
          <text class="picker-title">选择银行机构</text>
          <text class="picker-close" @click="showBankPicker = false">×</text>
        </view>
        <scroll-view class="picker-list" scroll-y>
          <view 
            class="picker-item" 
            v-for="bank in bankList" 
            :key="bank.id"
            @click="selectBank(bank)"
          >
            <text class="bank-name">{{ bank.name }}</text>
            <text class="bank-contact">{{ bank.contact_person }} - {{ bank.phone }}</text>
          </view>
        </scroll-view>
      </view>
    </view>
    
    <!-- 自定义TabBar -->
    <custom-tab-bar />
  </view>
</template>

<script>
import { api } from '@/utils/api'
import CustomTabBar from '@/custom-tab-bar/index.vue'

export default {
  components: {
    CustomTabBar
  },
  data() {
    return {
      consultationType: 'business_consult',
      selectedBank: null,
      showBankPicker: false,
      bankList: [],
      formData: {
        contact_name: '',
        contact_phone: '',
        content: ''
      },
      errors: {}
    }
  },
  computed: {
    canSubmit() {
      const { contact_name, contact_phone, content } = this.formData
      
      // 详细的字段验证
      const nameValid = contact_name.trim().length >= 2 && contact_name.trim().length <= 20
      const phoneValid = /^1[3-9]\d{9}$/.test(contact_phone.trim())
      const contentValid = content.trim().length >= 10 && content.trim().length <= 600
      
      // 银行选择验证（政策需求时必须选择银行）
      const bankValid = this.consultationType === 'business_consult' || this.selectedBank
      
      // 没有错误信息
      const noErrors = Object.values(this.errors).every(error => !error)
      
      return nameValid && phoneValid && contentValid && bankValid && noErrors
    }
  },
  onLoad() {
    this.loadBankList()
  },
  onShow() {
    if (typeof this.$root.$mp.page.getTabBar === 'function' && this.$root.$mp.page.getTabBar()) {
      this.$root.$mp.page.getTabBar().$vm.updateSelected(2)
    }
  },
  methods: {
    selectType(type) {
      this.consultationType = type
      if (type === 'business_consult') {
        this.selectedBank = null
      }
      // 清除错误信息
      this.errors = {}
    },

    selectBank(bank) {
      this.selectedBank = bank
      this.showBankPicker = false
    },

    async loadBankList() {
      try {
        const res = await api.getBanks()
        this.bankList = res.data?.items || []
      } catch (error) {
        console.error('加载银行列表失败:', error)
        // 模拟数据
        this.bankList = [
          { id: 1, name: '中国银行重庆分行', contact_person: '张经理', phone: '023-********' },
          { id: 2, name: '建设银行重庆分行', contact_person: '李经理', phone: '023-********' },
          { id: 3, name: '工商银行重庆分行', contact_person: '王经理', phone: '023-********' },
          { id: 4, name: '农业银行重庆分行', contact_person: '刘经理', phone: '023-********' },
          { id: 5, name: '交通银行重庆分行', contact_person: '陈经理', phone: '023-********' }
        ]
      }
    },

    async submitConsultation() {
      // 如果按钮被禁用，直接返回
      if (!this.canSubmit) {
        return
      }
      
      // 进行全面校验
      if (!this.validateAll()) {
        uni.showToast({
          title: '请检查并完善表单信息',
          icon: 'none'
        })
        return
      }

      try {
        uni.showLoading({ title: '提交中...' })

        // 准备提交数据（暂时不包含用户ID）
        const submitData = {
          contact_name: this.formData.contact_name.trim(),
          contact_phone: this.formData.contact_phone.trim(),
          content: this.formData.content.trim(),
          type: this.consultationType
        }

        // 如果是政策需求，添加银行ID
        if (this.consultationType === 'policy_demand' && this.selectedBank) {
          submitData.bank_id = this.selectedBank.id
        }

        console.log('提交的数据:', submitData)
        
        // 调用API提交数据
        const response = await api.submitInquiry(submitData)
        console.log('提交响应:', response)

        uni.hideLoading()
        uni.showToast({
          title: '提交成功',
          icon: 'success',
          duration: 2000
        })

        // 重置表单和错误信息
        this.formData = {
          contact_name: '',
          contact_phone: '',
          content: ''
        }
        this.errors = {}
        this.selectedBank = null
        this.consultationType = 'business_consult'

      } catch (error) {
        uni.hideLoading()
        console.error('提交咨询失败:', error)
        
        // 根据错误类型显示不同的提示
        let errorMessage = '提交失败，请重试'
        if (error.message) {
          errorMessage = error.message
        } else if (error.data && error.data.message) {
          errorMessage = error.data.message
        }
        
        uni.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 3000
        })
      }
    },

    validateName() {
      const name = this.formData.contact_name.trim()
      if (!name) {
        this.errors.contact_name = '请输入联系人姓名'
      } else if (name.length < 2) {
        this.errors.contact_name = '姓名至少需要2个字符'
      } else if (name.length > 20) {
        this.errors.contact_name = '姓名不能超过20个字符'
      } else {
        this.errors.contact_name = ''
      }
    },

    validatePhone() {
      const phone = this.formData.contact_phone.trim()
      const phoneRegex = /^1[3-9]\d{9}$/
      if (!phone) {
        this.errors.contact_phone = '请输入联系电话'
      } else if (!phoneRegex.test(phone)) {
        this.errors.contact_phone = '请输入正确的手机号码'
      } else {
        this.errors.contact_phone = ''
      }
    },

    validateContent() {
      const content = this.formData.content.trim()
      if (!content) {
        this.errors.content = '请输入咨询内容'
      } else if (content.length < 10) {
        this.errors.content = '咨询内容至少需要10个字符'
      } else if (content.length > 600) {
        this.errors.content = '咨询内容不能超过600个字符'
      } else {
        this.errors.content = ''
      }
    },

    validateBank() {
      if (this.consultationType === 'policy_demand' && !this.selectedBank) {
        uni.showToast({
          title: '请选择银行机构',
          icon: 'none'
        })
        return false
      }
      return true
    },

    validateAll() {
      this.validateName()
      this.validatePhone()
      this.validateContent()
      
      const hasErrors = Object.values(this.errors).some(error => error !== '')
      return !hasErrors && this.validateBank()
    }
  }
}
</script>

<style lang="scss" scoped>
/* 通用flex类 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.align-center {
  align-items: center;
}

.consultation-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #1E7DFA 0%, #1E7DFA 160rpx, #4A9BFD 220rpx, #8BB8FE 280rpx, #F2F5F7 340rpx);
  padding-bottom: 140rpx; /* 为自定义tabBar留出空间 */
  position: relative;
}

/* 顶部背景图片 */
.consultation-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 300rpx;
  background-image: url('/static/images/page-header-bg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 1;
  opacity: 0.3; /* 降低背景图片透明度，让渐变更明显 */
}

.section {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
  z-index: 3;
}

.section-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 咨询类型区域样式 */
.box_2 {
  margin: 0 30rpx 30rpx;
  margin-top: 0;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
  z-index: 3;
  padding: 30rpx;
}

.group_2 {
  align-items: center;
  margin-bottom: 30rpx;
}

.group_3 {
  width: 6rpx;
  height: 32rpx;
  background: #1E7DFA;
  border-radius: 3rpx;
  margin-right: 16rpx;
}

.text_2 {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}

.group_4 {
  gap: 20rpx;
}

.section_1,
.section_2 {
  flex: 1;
  background: #F8F9FA;
  border: 2rpx solid #E5E7EB;
  border-radius: 16rpx;
  padding: 24rpx 16rpx;
  min-height: 320rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.section_1.active,
.section_2.active {
  border-color: #1E7DFA;
  background: #1E7DFA;
  box-shadow: 0 4rpx 12rpx rgba(30, 125, 250, 0.15);
}

.section_1.active .text_3,
.section_1.active .text_4,
.section_2.active .text_5,
.section_2.active .text_6 {
  color: white;
}

.image-text_1,
.image-text_2 {
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 16rpx;
  width: 100%;
}

.label_1,
.label_2 {
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  display: block;
  margin: 0 auto;
}

.text-group_1,
.text-group_2 {
  gap: 8rpx;
  text-align: center;
  width: 100%;
}

.text_3,
.text_5 {
  font-size: 32rpx;
  font-weight: bold;
  color: #1F2937;
  line-height: 1.2;
  text-align: center;
  transition: color 0.3s ease;
}

.text_4,
.text_6 {
  font-size: 24rpx;
  color: #6B7280;
  line-height: 1.4;
  text-align: center;
  transition: color 0.3s ease;
}

/* 银行机构区域样式 */
.box_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12rpx;
  width: 702rpx;
  height: 224rpx;
  margin: 30rpx 24rpx 30rpx 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 3;
}

.bank_block_3 {
  width: 150rpx;
  height: 88rpx;
  margin-left: 32rpx;
  align-items: center;
}

.bank_block_4 {
  background-color: rgba(31, 115, 255, 1);
  width: 8rpx;
  height: 32rpx;
  margin-top: 28rpx;
  margin-right: 16rpx;
  border-radius: 4rpx;
}

.bank_text_7 {
  width: 128rpx;
  height: 88rpx;
  overflow-wrap: break-word;
  color: rgba(26, 32, 44, 1);
  font-size: 32rpx;
  font-weight: 600;
  text-align: left;
  line-height: 88rpx;
}

.block_5 {
  background-color: rgba(242, 245, 247, 1);
  border-radius: 8rpx;
  width: 638rpx;
  height: 88rpx;
  margin: 16rpx 0 32rpx 32rpx;
  align-items: center;
  justify-content: space-between;
  padding: 0 22rpx 0 22rpx;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.block_5:active {
  background-color: rgba(230, 235, 240, 1);
}

.bank_text_8 {
  color: rgba(177, 181, 188, 1);
  font-size: 28rpx;
  font-weight: normal;
  text-align: left;
  line-height: 56rpx;
  flex: 1;
}

.bank_text_8:not(:empty) {
  color: rgba(26, 32, 44, 1);
}

.thumbnail_1 {
  width: 32rpx;
  height: 32rpx;
}

/* 咨询信息区域样式 */
.group_5 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12rpx;
  position: relative;
  width: 702rpx;
  margin: 30rpx 24rpx 30rpx 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  z-index: 3;
  padding-bottom: 32rpx;
}

.block_1 {
  width: 150rpx;
  height: 88rpx;
  margin-left: 32rpx;
  align-items: center;
}

.box_3 {
  background-color: rgba(31, 115, 255, 1);
  width: 8rpx;
  height: 32rpx;
  margin-top: 28rpx;
  margin-right: 16rpx;
  border-radius: 4rpx;
}

.text_7 {
  width: 128rpx;
  height: 88rpx;
  overflow-wrap: break-word;
  color: rgba(26, 32, 44, 1);
  font-size: 32rpx;
  font-weight: 600;
  text-align: left;
  line-height: 88rpx;
}

.text-wrapper_1,
.text-wrapper_3,
.text-wrapper_5 {
  height: 40rpx;
  overflow-wrap: break-word;
  font-size: 0;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 38rpx;
  margin-left: 30rpx;
  margin-top: 16rpx;
}

.text-wrapper_3,
.text-wrapper_5 {
  margin-top: 52rpx;
}

.text_8,
.text_11,
.text_14 {
  color: rgba(236, 48, 48, 1);
  font-size: 28rpx;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 38rpx;
  margin-right: 8rpx;
}

.text_9,
.text_12,
.text_15 {
  color: rgba(22, 23, 26, 1);
  font-size: 28rpx;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 38rpx;
}

.block_2,
.block_3 {
  background-color: rgba(242, 245, 247, 1);
  border-radius: 8rpx;
  height: 88rpx;
  width: 638rpx;
  margin: 12rpx 0 0 32rpx;
}

.text-wrapper_2,
.text-wrapper_4 {
  background-color: rgba(242, 245, 247, 1);
  border-radius: 8rpx;
  height: 88rpx;
  width: 638rpx;
  justify-content: center;
}

.text_10,
.text_13 {
  width: 100%;
  height: 56rpx;
  color: rgba(177, 181, 188, 1);
  font-size: 28rpx;
  font-weight: normal;
  text-align: left;
  line-height: 56rpx;
  margin: 16rpx 0 0 22rpx;
  background: transparent;
  border: none;
  outline: none;
  padding: 0;
}

.text_10:not(:placeholder-shown),
.text_13:not(:placeholder-shown) {
  color: rgba(26, 32, 44, 1);
}

.text-wrapper_6 {
  background-image: linear-gradient(90deg, rgba(49, 120, 249, 1) 0%, rgba(51, 197, 255, 1) 100%) !important;
  border-radius: 22rpx;
  height: 88rpx;
  width: 638rpx;
  margin: 60rpx 0 32rpx 32rpx;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  background: none;
}

.text-wrapper_6:active:not(.disabled) {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(49, 120, 249, 0.3);
}

.text-wrapper_6.disabled {
  opacity: 0.5;
  background-image: linear-gradient(90deg, #ccc 0%, #ccc 100%) !important;
  cursor: not-allowed;
  transform: none;
}

.text_16 {
  color: rgba(255, 255, 255, 1);
  font-size: 32rpx;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 44rpx;
}

.text-wrapper_7 {
  background-color: rgba(242, 245, 247, 1);
  border-radius: 8rpx;
  width: 638rpx;
  height: 328rpx;
  margin: 12rpx 0 0 32rpx;
  justify-content: flex-start;
  position: relative;
  overflow: hidden;
}

.form-textarea-new {
  width: 594rpx;
  height: 200rpx;
  color: rgba(177, 181, 188, 1);
  font-size: 28rpx;
  font-weight: normal;
  text-align: left;
  line-height: 1.5;
  margin: 24rpx 0 0 22rpx;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  padding: 0;
  box-sizing: border-box;
}

.form-textarea-new::-webkit-input-placeholder {
  color: rgba(177, 181, 188, 1);
}

.form-textarea-new::-moz-placeholder {
  color: rgba(177, 181, 188, 1);
}

.form-textarea-new:-ms-input-placeholder {
  color: rgba(177, 181, 188, 1);
}

.form-textarea-new:not(:placeholder-shown) {
  color: rgba(26, 32, 44, 1);
}

.text_18 {
  width: 70rpx;
  height: 56rpx;
  color: rgba(147, 152, 160, 1);
  font-size: 24rpx;
  font-weight: normal;
  text-align: right;
  line-height: 56rpx;
  position: absolute;
  bottom: 24rpx;
  right: 22rpx;
}

.error-text {
  color: rgba(236, 48, 48, 1);
  font-size: 24rpx;
  margin: 8rpx 0 0 32rpx;
  line-height: 1.4;
}

/* 错误状态的边框应用到容器上 */
.block_2.error,
.block_3.error {
  border: 2rpx solid rgba(236, 48, 48, 1);
}

.text-wrapper_7.error {
  border: 2rpx solid rgba(236, 48, 48, 1);
}

/* 温馨提示区域样式 */
.group_6 {
  width: 750rpx;
  height: 240rpx;
  margin-bottom: 10rpx;
  background: transparent;
}

.group_6 .box_4 {
  width: 126rpx;
  height: 48rpx;
  margin: 24rpx 0 0 24rpx;
  align-items: center;
  background: transparent;
}

.group_6 .thumbnail_1 {
  width: 24rpx;
  height: 24rpx;
  margin-top: 12rpx;
  margin-right: 6rpx;
}

.text_19 {
  width: 96rpx;
  height: 48rpx;
  overflow-wrap: break-word;
  color: rgba(236, 139, 48, 1);
  font-size: 24rpx;
  font-weight: 600;
  text-align: left;
  line-height: 48rpx;
}

.paragraph_1 {
  width: 706rpx;
  height: 192rpx;
  overflow-wrap: break-word;
  color: rgba(147, 152, 160, 1);
  font-size: 24rpx;
  font-weight: normal;
  text-align: left;
  line-height: 48rpx;
  margin: 0 0 26rpx 22rpx;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.bank-picker {
  width: 600rpx;
  max-height: 800rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.picker-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.picker-close {
  font-size: 40rpx;
  color: #999;
}

.picker-list {
  max-height: 600rpx;
}

.picker-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.picker-item:last-child {
  border-bottom: none;
}

.bank-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.bank-contact {
  font-size: 26rpx;
  color: #666;
}


</style> 