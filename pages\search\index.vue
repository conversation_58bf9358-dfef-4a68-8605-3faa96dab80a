<template>
  <view class="search-page">
    <!-- 搜索头部 -->
    <view class="search-header">
      <view class="section_2 flex-row">
        <view class="image-text_1 flex-row justify-between">
          <image
            class="thumbnail_1"
            referrerpolicy="no-referrer"
            src="/static/images/search-placeholder-icon.png"
          />
          <input
            v-model="searchKeyword"
            placeholder="搜索政策文件、问题、新闻、政策解读"
            class="text-group_1"
            @confirm="performSearch()"
            focus
          />
        </view>
        <view class="text-wrapper_1 flex-col" @click="performSearch()">
          <text class="text_2">搜索</text>
        </view>
      </view>
    </view>

    <!-- 搜索类型选择 -->
    <view class="search-types">
      <view class="section_3 flex-row">
        <view class="tab-item">
          <text
            class="text_3"
            :class="{ active: currentType === 'policy' }"
            @click="switchSearchType('policy')"
          >政策</text>
          <view
            class="block_2 flex-col"
            :class="{ active: currentType === 'policy' }"
          ></view>
        </view>
        <view class="tab-item">
          <text
            class="text_4"
            :class="{ active: currentType === 'news' }"
            @click="switchSearchType('news')"
          >新闻</text>
          <view
            class="block_2 flex-col"
            :class="{ active: currentType === 'news' }"
          ></view>
        </view>
        <view class="tab-item">
          <text
            class="text_5"
            :class="{ active: currentType === 'faq' }"
            @click="switchSearchType('faq')"
          >问题</text>
          <view
            class="block_2 flex-col"
            :class="{ active: currentType === 'faq' }"
          ></view>
        </view>
        <view class="tab-item">
          <text
            class="text_6"
            :class="{ active: currentType === 'interpretation' }"
            @click="switchSearchType('interpretation')"
          >解读</text>
          <view
            class="block_2 flex-col"
            :class="{ active: currentType === 'interpretation' }"
          ></view>
        </view>
      </view>
    </view>

    <!-- 统一的内容展示区域 -->
    <view class="content-display">

      <!-- 列表标题 -->
      <view class="block_3 flex-row" v-if="!hasSearched">
        <view class="image-text_2 flex-row justify-between">
          <image
            class="label_2"
            referrerpolicy="no-referrer"
            src="/static/images/hot-category-bg.png"
          />
          <text class="text-group_2">{{ getHotTypeTitle(currentType) }}</text>
        </view>
      </view>

      <!-- 政策文件展示 -->
      <view v-if="currentType === 'policy' && currentDisplayData.length > 0" class="result-section">
        <view class="block_3 flex-row" v-if="hasSearched">
          <view class="image-text_2 flex-row justify-between">
            <image
              class="label_2"
              referrerpolicy="no-referrer"
              src="/static/images/hot-category-bg.png"
            />
            <text class="text-group_2">热门政策</text>
          </view>
        </view>
        <view class="result-list">
          <view
            v-for="item in currentDisplayData"
            :key="item.id"
            class="block_4 flex-col justify-end"
            @click="gotoDetail('policy', item.id)"
          >
            <view class="box_1 flex-row justify-between">
              <view class="group_3 flex-col">
                <view class="text-wrapper_2 flex-col">
                  <text class="text_7">{{ item.category1 || '政策文件' }}</text>
                </view>
              </view>
              <text class="text_8" v-html="getDisplayTitle(item)"></text>
            </view>
            <text class="text_9">
              {{ truncateText(getDisplayContent(item), hasSearched ? 100 : 120) }}
            </text>
            <view class="box_2 flex-row">
              <text class="text_10">{{ formatDate(item.publish_date) }}</text>
              <image
                class="thumbnail_2"
                referrerpolicy="no-referrer"
                src="/static/images/view-icon.png"
              />
              <text class="text_11">{{ item.view_count || 0 }}</text>
              <image
                class="thumbnail_3"
                referrerpolicy="no-referrer"
                src="/static/images/like-icon-new.png"
              />
              <text class="text_12">{{ item.like_count || 0 }}</text>
              <image
                class="thumbnail_4"
                referrerpolicy="no-referrer"
                src="/static/images/share-icon-new.png"
              />
              <text class="text_13">{{ item.forward_count || 0 }}</text>
            </view>
            <view class="box_3 flex-col"></view>
          </view>
        </view>

        <!-- 没有更多数据提示 -->
        <view class="no-more-data" v-if="!hasMoreData && currentDisplayData.length > 0">
          <text class="no-more-text">没有更多数据了</text>
        </view>
      </view>

      <!-- 新闻展示 -->
      <view v-if="currentType === 'news' && currentDisplayData.length > 0" class="result-section">
        <view class="block_3 flex-row" v-if="hasSearched">
          <view class="image-text_2 flex-row justify-between">
            <image
              class="label_2"
              referrerpolicy="no-referrer"
              src="/static/images/hot-category-bg.png"
            />
            <text class="text-group_2">热门新闻</text>
          </view>
        </view>
        <view class="result-list">
          <view
            v-for="item in currentDisplayData"
            :key="item.id"
            class="block_4 flex-col justify-end"
            @click="gotoDetail('news', item.id)"
          >
            <view class="box_1 flex-row justify-between">
              <view class="group_3 flex-col">
                <view class="text-wrapper_2 flex-col">
                  <text class="text_7">{{ item.category || '新闻动态' }}</text>
                </view>
              </view>
              <text class="text_8" v-html="getDisplayTitle(item)"></text>
            </view>
            <text class="text_9">
              {{ truncateText(getDisplayContent(item), hasSearched ? 100 : 120) }}
            </text>
            <view class="box_2 flex-row">
              <text class="text_10">{{ formatDate(item.publish_date) }}</text>
              <image
                class="thumbnail_2"
                referrerpolicy="no-referrer"
                src="/static/images/view-icon.png"
              />
              <text class="text_11">{{ item.view_count || 0 }}</text>
              <image
                class="thumbnail_3"
                referrerpolicy="no-referrer"
                src="/static/images/like-icon-new.png"
              />
              <text class="text_12">{{ item.like_count || 0 }}</text>
              <image
                class="thumbnail_4"
                referrerpolicy="no-referrer"
                src="/static/images/share-icon-new.png"
              />
              <text class="text_13">{{ item.forward_count || 0 }}</text>
            </view>
            <view class="box_3 flex-col"></view>
          </view>
        </view>

        <!-- 没有更多数据提示 -->
        <view class="no-more-data" v-if="!hasMoreData && currentDisplayData.length > 0">
          <text class="no-more-text">没有更多数据了</text>
        </view>
      </view>

      <!-- FAQ展示 -->
      <view v-if="currentType === 'faq' && currentDisplayData.length > 0" class="result-section">
        <view class="block_3 flex-row" v-if="hasSearched">
          <view class="image-text_2 flex-row justify-between">
            <image
              class="label_2"
              referrerpolicy="no-referrer"
              src="/static/images/hot-category-bg.png"
            />
            <text class="text-group_2">热门问题</text>
          </view>
        </view>
        <view class="result-list">
          <view
            v-for="item in currentDisplayData"
            :key="item.id"
            class="block_4 flex-col justify-end"
            @click="gotoDetail('faq', item.id)"
          >
            <view class="box_1 flex-row justify-between">
              <view class="group_3 flex-col">
                <view class="text-wrapper_2 flex-col">
                  <text class="text_7">{{ item.category || '常见问题' }}</text>
                </view>
              </view>
              <text class="text_8" v-html="getDisplayTitle(item)"></text>
            </view>
            <text class="text_9">
              {{ truncateText(getDisplayContent(item), hasSearched ? 100 : 120) }}
            </text>
            <view class="box_2 flex-row">
              <text class="text_10">{{ formatDate(item.answer_date || item.publish_date) }}</text>
              <image
                class="thumbnail_2"
                referrerpolicy="no-referrer"
                src="/static/images/view-icon.png"
              />
              <text class="text_11">{{ item.view_count || 0 }}</text>
              <image
                class="thumbnail_3"
                referrerpolicy="no-referrer"
                src="/static/images/like-icon-new.png"
              />
              <text class="text_12">{{ item.like_count || 0 }}</text>
              <image
                class="thumbnail_4"
                referrerpolicy="no-referrer"
                src="/static/images/share-icon-new.png"
              />
              <text class="text_13">{{ item.forward_count || 0 }}</text>
            </view>
            <view class="box_3 flex-col"></view>
          </view>
        </view>

        <!-- 没有更多数据提示 -->
        <view class="no-more-data" v-if="!hasMoreData && currentDisplayData.length > 0">
          <text class="no-more-text">没有更多数据了</text>
        </view>
      </view>

      <!-- 政策解读展示 -->
      <view v-if="currentType === 'interpretation' && currentDisplayData.length > 0" class="result-section">
        <view class="block_3 flex-row" v-if="hasSearched">
          <view class="image-text_2 flex-row justify-between">
            <image
              class="label_2"
              referrerpolicy="no-referrer"
              src="/static/images/hot-category-bg.png"
            />
            <text class="text-group_2">热门解读</text>
          </view>
        </view>
        <view class="result-list">
          <view
            class="list-items_1 flex-col justify-end"
            v-for="(item, index) in currentDisplayData"
            :key="item.id"
            @click="gotoDetail('interpretation', item.id)"
          >
            <view class="box_2 flex-row justify-between">
              <view class="image-wrapper_1 flex-col">
                <image
                  v-if="getVideoThumbnail(item)"
                  class="label_3"
                  referrerpolicy="no-referrer"
                  :src="getVideoThumbnail(item)"
                  mode="aspectFill"
                  @error="onVideoThumbnailError(item)"
                />
                <view
                  v-else
                  class="label_3 placeholder-bg"
                  :class="'interpretation-bg-' + (index % 6 + 1)"
                >
                  <view v-if="item.video_url" class="play-icon-small">
                    <text>▶</text>
                  </view>
                  <view v-else class="text-icon-small">
                    <text>📄</text>
                  </view>
                </view>
              </view>
              <view class="box_3 flex-col">
                <view class="section_3 flex-row justify-between">
                  <view class="section_4 flex-col">
                    <view class="text-wrapper_3 flex-col">
                      <text class="text_7">{{ item.category || '政策解读' }}</text>
                    </view>
                  </view>
                  <text class="text_8" v-html="getDisplayTitle(item)"></text>
                </view>
                <text class="text_9">{{ truncateText(getDisplayContent(item), hasSearched ? 100 : 120) }}</text>
                <view class="section_5 flex-row">
                  <image
                    class="thumbnail_2"
                    referrerpolicy="no-referrer"
                    src="/static/images/view-icon.png"
                  />
                  <text class="text_10">{{ item.view_count || 0 }}</text>
                  <image
                    class="thumbnail_3"
                    referrerpolicy="no-referrer"
                    src="/static/images/like-icon-new.png"
                  />
                  <text class="text_11">{{ item.like_count || 0 }}</text>
                  <image
                    class="thumbnail_4"
                    referrerpolicy="no-referrer"
                    src="/static/images/share-icon-new.png"
                  />
                  <text class="text_12">{{ item.forward_count || 0 }}</text>
                </view>
              </view>
            </view>
            <view class="box_4 flex-col"></view>
          </view>
        </view>
		<!-- 没有更多数据提示 -->
		<view class="no-more-data" v-if="!hasMoreData && currentDisplayData.length > 0">
		  <text class="no-more-text">没有更多数据了</text>
		</view>
	  </view>

      <!-- 加载更多区域 -->
      <view class="load-more-section" v-if="currentDisplayData.length > 0">
        <!-- 加载更多按钮 -->
        <view class="load-more" v-if="hasMoreData && !isLoading" @click="loadMoreData">
          <text class="load-more-text">{{ hasSearched ? '加载更多搜索结果' : '加载更多' }}</text>
          <text class="load-more-icon">↓</text>
        </view>

        <!-- 加载中状态 -->
        <view class="loading-more" v-if="isLoading">
          <text class="loading-more-text">{{ hasSearched ? '正在搜索...' : '正在加载...' }}</text>
          <text class="loading-more-icon">⏳</text>
        </view>
      </view>

      <!-- 初始加载状态 -->
      <view class="loading-state" v-if="currentDisplayData.length === 0 && isLoading">
        <view class="loading-icon">⏳</view>
        <view class="loading-text">{{ hasSearched ? '正在搜索...' : `正在加载${getTypeTitle(currentType)}...` }}</view>
      </view>
    </view>
  </view>
</template>

<script>
import api from '@/utils/api.js'

export default {
  data() {
    return {
      searchKeyword: '',
      currentType: 'policy',
      hasSearched: false,
      searchResults: {},
      totalResults: 0,
      categoryData: [], // 类型数据

      // 分页相关状态
      currentPage: 1,
      hasMoreData: true,
      isLoading: false,
      totalPages: 1,

      searchTypes: [
        { label: '政策', value: 'policy' },
        { label: '新闻', value: 'news' },
        { label: '问题', value: 'faq' },
        { label: '解读', value: 'interpretation' }
      ]
    }
  },

  computed: {
    // 当前显示的数据（统一搜索结果和分类数据）
    currentDisplayData() {
      if (this.hasSearched) {
        // 搜索模式：从搜索结果中获取当前类型的数据
        switch (this.currentType) {
          case 'policy':
            return this.searchResults.policies || []
          case 'news':
            return this.searchResults.news || []
          case 'faq':
            return this.searchResults.faq || []
          case 'interpretation':
            return this.searchResults.interpretations || []
          default:
            return []
        }
      } else {
        // 浏览模式：使用分类数据
        return this.categoryData || []
      }
    }
  },

  onLoad(options) {
    try {
      if (options && options.type) {
        const validTypes = this.searchTypes.map(t => t.value)
        if (validTypes.includes(options.type)) {
          this.currentType = options.type
        }
      }

      // 页面加载时自动加载当前类型的数据
      console.log('搜索页面加载完成，当前类型:', this.currentType)
      this.loadCategoryData(this.currentType).catch(err => {
        console.error('分类数据加载失败:', err)
      })
    } catch (err) {
      console.error('onLoad错误:', err)
    }
  },

  methods: {
    async performSearch() {
      if (!this.searchKeyword.trim()) {
        // 当搜索词为空时，重置为分类列表视图
        this.hasSearched = false
        this.searchResults = {}
        this.totalResults = 0
        // 加载当前类型的分类数据
        await this.loadCategoryData(this.currentType, false)
        return
      }

      // 如果是新搜索（不是加载更多），重置页码
      if (!this.isLoading) {
        this.currentPage = 1
        this.hasMoreData = true
      }

      this.hasSearched = true
      this.isLoading = true

      try {
        const params = {
          q: this.searchKeyword,  // 根据API文档使用q参数
          type: 'all',
          page: this.currentPage,
          per_page: 20
        }

        console.log('搜索参数:', params)
        const res = await api.search(params)
        console.log('搜索响应:', res)
        
        // 根据API文档，搜索结果在data.items中，需要按类型分组
        const items = res.data?.items || []
        console.log('搜索原始数据:', items)
        
        const newGroupedResults = this.groupSearchResults(items)
        console.log('分组后的新搜索结果:', newGroupedResults)
        
        if (this.currentPage === 1) {
          // 第一页，替换结果
          this.searchResults = newGroupedResults
        } else {
          // 后续页，合并结果
          this.mergeSearchResults(newGroupedResults)
        }
        
        console.log('最终搜索结果:', this.searchResults)
        
        // 更新分页信息
        this.totalResults = res.data?.total || 0
        this.totalPages = res.data?.pages || 1
        this.hasMoreData = this.currentPage < this.totalPages
        
        if (this.currentPage === 1) {
          // 首次搜索后，默认选中第一个tab
          this.currentType = 'policy';
        }
        
        console.log('搜索结果总数:', this.totalResults, '当前页:', this.currentPage, '总页数:', this.totalPages)

      } catch (error) {
        console.error('搜索失败:', error)
        uni.showToast({
          title: '搜索失败',
          icon: 'none'
        })
      } finally {
        this.isLoading = false
      }
    },

    groupSearchResults(items) {
      const grouped = {
        policies: [],
        news: [],
        faq: [],
        interpretations: []
      }
      
      items.forEach(item => {
        switch (item.type) {
          case 'policy':
            grouped.policies.push({
              id: item.id,
              title: item.title,
              content: item.content,
              category: item.category || '热门政策',
              publish_date: item.publish_date || item.created_at
            })
            break
          case 'news':
            grouped.news.push({
              id: item.id,
              title: item.title,
              content: item.content,
              category: item.category || '热门新闻',
              publish_date: item.publish_date || item.created_at
            })
            break
          case 'faq':
            grouped.faq.push({
              id: item.id,
              question: item.title,
              answer: item.content,
              category: item.category || '热门问题',
              answer_date: item.publish_date || item.created_at
            })
            break
          case 'interpretation':
            grouped.interpretations.push({
              id: item.id,
              title: item.title,
              content: item.content,
              category: item.category || '热门解读',
              publish_date: item.publish_date || item.created_at,
              video_url: item.video_url
            })
            break
        }
      })
      
      return grouped
    },

    async switchSearchType(type) {
      this.currentType = type
      
      // 只有在非搜索结果视图下，切换tab才重新加载分类列表
      if (!this.hasSearched) {
        this.hasSearched = false
        await this.loadCategoryData(type, false)
      }
    },

    async loadCategoryData(type, isLoadMore = false) {
      console.log('开始加载分类数据，类型:', type, '是否加载更多:', isLoadMore)
      
      if (!isLoadMore) {
        // 重置分页状态
        this.currentPage = 1
        this.hasMoreData = true
        this.categoryData = []
      }
      
      this.isLoading = true
      
      try {
        let response = null
        const params = { 
          page: this.currentPage, 
          per_page: 10 
        }
        
        switch (type) {
          case 'policy':
            console.log('正在请求政策数据...', params)
            response = await api.getPolicies(params)
            console.log('政策数据响应:', response)
            break
          case 'news':
            console.log('正在请求新闻数据...', params)
            response = await api.getNews(params)
            console.log('新闻数据响应:', response)
            break
          case 'faq':
            console.log('正在请求FAQ数据...', params)
            response = await api.getFaqs(params)
            console.log('FAQ数据响应:', response)
            break
          case 'interpretation':
            console.log('正在请求政策解读数据...', params)
            response = await api.getInterpretations(params)
            console.log('政策解读数据响应:', response)
            break
        }
        
        if (response && response.data) {
          const newData = response.data.items || response.data.data || []
          console.log('解析后的新数据:', newData)
          
          // 更新分页信息
          this.totalPages = response.data.pages || 1
          this.hasMoreData = this.currentPage < this.totalPages
          
          if (isLoadMore) {
            // 追加数据
            this.categoryData = [...this.categoryData, ...newData]
          } else {
            // 替换数据
            this.categoryData = newData
          }
          
          console.log('当前页:', this.currentPage, '总页数:', this.totalPages, '是否有更多:', this.hasMoreData)
          console.log('最终设置的分类数据:', this.categoryData)
        }
      } catch (error) {
        console.error('加载分类数据失败:', error)
        
        // 显示错误提示
        uni.showToast({
          title: `加载${this.getTypeTitle(type)}失败`,
          icon: 'none',
          duration: 2000
        })
      } finally {
        this.isLoading = false
      }
    },

    getTypeIcon(type) {
      const icons = {
        policy: '📋',
        news: '📰',
        faq: '❓',
        interpretation: '🎥'
      }
      return icons[type] || '📄'
    },

    getTypeTitle(type) {
      const titles = {
        policy: '政策文件',
        news: '要闻动态',
        faq: '常见问题',
        interpretation: '政策解读'
      }
      return titles[type] || '全部内容'
    },

    getHotTypeTitle(type) {
      const titles = {
        policy: '热门政策',
        news: '热门新闻',
        faq: '热门问题',
        interpretation: '热门解读'
      }
      return titles[type] || '热门内容'
    },

    gotoDetail(type, id) {
      let url = ''
      switch (type) {
        case 'policy':
          url = `/pages/policy/detail?id=${id}`
          break
        case 'news':
          url = `/pages/news/detail?id=${id}`
          break
        case 'faq':
          url = `/pages/faq/detail?id=${id}`
          break
        case 'interpretation':
          url = `/pages/interpretation/detail?id=${id}`
          break
      }
      
      if (url) {
        uni.navigateTo({ url })
      }
    },

    highlightKeyword(text) {
      if (!this.searchKeyword || !text) return text
      const regex = new RegExp(this.searchKeyword.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'), 'gi');
      return text.replace(regex, `<span style="color: #1E90FF; font-weight: bold;">${this.searchKeyword}</span>`);
    },

    truncateText(text, maxLength) {
      if (!text) return ''
      // 移除HTML标签
      const plainText = text.replace(/<[^>]*>/g, '')
      return plainText.length > maxLength 
        ? plainText.substring(0, maxLength) + '...'
        : plainText
    },

    formatDate(dateStr) {
      if (!dateStr) return ''
      return new Date(dateStr).toLocaleDateString()
    },

    // 统一的加载更多方法
    async loadMoreData() {
      if (this.isLoading || !this.hasMoreData) return

      this.currentPage++

      if (this.hasSearched) {
        // 搜索模式：加载更多搜索结果
        await this.performSearch()
      } else {
        // 浏览模式：加载更多分类数据
        await this.loadCategoryData(this.currentType, true)
      }
    },

    // 获取显示标题（支持搜索高亮）
    getDisplayTitle(item) {
      const title = item.title || item.question || ''
      return this.hasSearched ? this.highlightKeyword(title) : title
    },

    // 获取显示内容
    getDisplayContent(item) {
      return item.content || item.answer || ''
    },

    // 新增：合并搜索结果
    mergeSearchResults(newResults) {
      Object.keys(newResults).forEach(key => {
        if (this.searchResults[key]) {
          this.searchResults[key] = [...this.searchResults[key], ...newResults[key]]
        } else {
          this.searchResults[key] = newResults[key]
        }
      })
    },
    
    getVideoThumbnail(item) {
      if (item && (item.thumbnail || item.cover_image)) {
        return item.thumbnail || item.cover_image
      }
      if (item && item.video_url) {
        if (item.video_url.includes('example.com')) {
          return item.video_url.replace('.mp4', '_thumb.jpg')
        }
      }
      return ''
    },

    onVideoThumbnailError(item) {
      console.log(`视频缩略图加载失败:`, item)
      this.$set(item, 'thumbnail_error', true)
    },
  }
}
</script>

<style lang="scss" scoped>
.search-page {
  background: linear-gradient(180deg, #fef5f7 0%, #f8f9fa 40%, #f1f3f5 100%);
  min-height: 80vh;
  position: relative;
}

/* 搜索框到分类tab区域背景渐变 */
.search-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 230rpx;
  background: linear-gradient(180deg, #1E7DFA 0%, #3AADFF 100%);
  z-index: 1;
}

/* 重庆山城剪影装饰 */
.search-page::after {
  content: '';
  position: absolute;
  top: 220rpx;
  left: 0;
  right: 0;
  height: 80rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 80'%3E%3Cpath d='M0,60 L50,55 L100,50 L150,45 L200,40 L250,45 L300,35 L350,40 L400,30 L450,35 L500,25 L550,30 L600,20 L650,25 L700,15 L750,20 L800,10 L850,15 L900,5 L950,10 L1000,0 L1050,5 L1100,0 L1150,5 L1200,0 L1200,80 L0,80 Z' fill='%23ffffff' fill-opacity='0.08'/%3E%3C/svg%3E") repeat-x;
  background-size: 1200rpx 80rpx;
  z-index: 2;
  opacity: 0.7;
}

// 通用flex样式
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.flex-col {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.search-header {
  background: transparent;
  padding: 25rpx 30rpx 15rpx;
  position: relative;
  z-index: 2;
}

.section_2 {
  background-color: rgba(242, 245, 247, 1);
  border-radius: 32px;
  width: 702rpx;
  height: 72rpx;
  margin: 24rpx auto 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .image-text_1 {
    width: 522rpx;
    height: 72rpx;
    margin-left: 32rpx;
    display: flex;
    align-items: center;
    gap: 14rpx;

    .thumbnail_1 {
      width: 32rpx;
      height: 32rpx;
      flex-shrink: 0;
    }

    .text-group_1 {
      flex: 1;
      height: 72rpx;
      overflow-wrap: break-word;
      color: rgba(147, 152, 160, 1);
      font-size: 28rpx;
      font-family: PingFang SC-Regular;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 72rpx;
      border: none;
      background: transparent;
      outline: none;

      &::placeholder {
        color: rgba(147, 152, 160, 1);
      }
    }
  }

  .text-wrapper_1 {
    background-color: rgba(31, 115, 255, 1);
    border-radius: 32px;
    height: 64rpx;
    width: 118rpx;
    margin-right: 4rpx;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:active {
      transform: scale(0.95);
      background-color: rgba(31, 115, 255, 0.8);
    }

    .text_2 {
      color: rgba(255, 255, 255, 1);
      font-size: 28rpx;
      font-family: PingFang SC-Regular;
      font-weight: normal;
      text-align: center;
      white-space: nowrap;
    }
  }
}

.search-types {
  padding: 0 30rpx 10rpx;
  background: transparent;
  position: relative;
  z-index: 3;
  display: flex;
  justify-content: center;
}

.section_3 {
  width: 706rpx;
  height: 88rpx;
  margin: 0;

  .tab-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .text_3 {
    width: 178rpx;
    height: 88rpx;
    overflow-wrap: break-word;
    color: rgba(255, 255, 255, 0.7);
    font-size: 32rpx;
    font-family: PingFang SC-Regular;
    font-weight: normal;
    text-align: center;
    line-height: 88rpx;
    transition: all 0.3s ease;

    &.active {
      color: rgba(255, 255, 255, 1);
      font-family: PingFang SC-Semibold;
      font-weight: 600;
    }

    &:active {
      transform: scale(0.95);
    }
  }

  .block_2 {
    background-color: transparent;
    width: 36rpx;
    height: 8rpx;
    position: absolute;
    bottom: 8rpx;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 4rpx;
    transition: all 0.3s ease;

    &.active {
      background-color: rgba(255, 255, 255, 1);
    }
  }

  .text_4, .text_5, .text_6 {
    width: 178rpx;
    height: 88rpx;
    overflow-wrap: break-word;
    color: rgba(255, 255, 255, 0.7);
    font-size: 32rpx;
    font-family: PingFang SC-Regular;
    font-weight: normal;
    text-align: center;
    line-height: 88rpx;
    transition: all 0.3s ease;

    &.active {
      color: rgba(255, 255, 255, 1);
      font-family: PingFang SC-Semibold;
      font-weight: 600;
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

.content-display {
  padding: 0;
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.block_3 {
  width: 700rpx;
  margin: 0 auto;
  height: 96rpx;
  background: url(/static/images/hot-category-header-bg.png) 100% no-repeat;
  background-size: 100% 100%;
  margin-bottom: 0;

  .image-text_2 {
    width: 190rpx;
    height: 96rpx;
    margin-left: 32rpx;

    .label_2 {
      width: 48rpx;
      height: 48rpx;
      margin-top: 24rpx;
    }

    .text-group_2 {
      width: 128rpx;
      height: 96rpx;
      overflow-wrap: break-word;
      color: rgba(31, 115, 255, 1);
      font-size: 32rpx;
      font-family: PingFang SC-Semibold;
      font-weight: 600;
      text-align: left;
      line-height: 96rpx;
    }
  }
}

.block_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 0;
  width: 702rpx;
  height: 224rpx;
  margin-bottom: 0;
  box-shadow: none;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 8rpx 40rpx rgba(30, 144, 255, 0.15);
  }

  &:first-child {
    border-radius: 0;
  }

  &:last-child {
    border-radius: 0 0 12rpx 12rpx;
  }

  .box_1 {
    width: 636rpx;
    height: 48rpx;
    margin: 32rpx 32rpx 0 32rpx;

    .group_3 {
      background-color: rgba(249, 249, 249, 1);
      height: 44rpx;
      margin-top: 2rpx;
      width: auto;
      min-width: 112rpx;

      .text-wrapper_2 {
        background-color: rgba(31, 115, 255, 0.1);
        border-radius: 4px;
        height: 44rpx;
        width: 100%;
        padding: 0 12rpx;

        .text_7 {
          width: 100%;
          height: 40rpx;
          overflow-wrap: break-word;
          color: rgba(31, 115, 255, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Semibold;
          font-weight: 600;
          text-align: center;
          white-space: nowrap;
          line-height: 40rpx;
          margin: 2rpx 0 0 0;
        }
      }
    }

    .text_8 {
      flex: 1;
      height: 48rpx;
      overflow-wrap: break-word;
      color: rgba(26, 32, 44, 1);
      font-size: 32rpx;
      font-family: PingFang SC-Semibold;
      font-weight: 600;
      text-align: left;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      line-height: 48rpx;
      margin-left: 16rpx;
    }
  }

  .text_9 {
    width: 636rpx;
    height: 32rpx;
    overflow-wrap: break-word;
    color: rgba(158, 158, 158, 1);
    font-size: 24rpx;
    font-family: PingFang SC-Regular;
    font-weight: normal;
    text-align: left;
    line-height: 32rpx;
    margin: 8rpx 32rpx 0 32rpx;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .box_2 {
    width: 636rpx;
    height: 32rpx;
    margin: 24rpx 32rpx 0 32rpx;

    .text_10 {
      width: 104rpx;
      height: 32rpx;
      overflow-wrap: break-word;
      color: rgba(147, 152, 160, 1);
      font-size: 24rpx;
      font-family: PingFang SC-Regular;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 32rpx;
    }

    .thumbnail_2 {
      width: 32rpx;
      height: 32rpx;
      margin-left: 252rpx;
    }

    .text_11 {
      width: 40rpx;
      height: 32rpx;
      overflow-wrap: break-word;
      color: rgba(144, 146, 149, 1);
      font-size: 24rpx;
      font-family: PingFang SC-Regular;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 32rpx;
      margin-left: 2rpx;
    }

    .thumbnail_3 {
      width: 32rpx;
      height: 32rpx;
      margin-left: 30rpx;
    }

    .text_12 {
      width: 40rpx;
      height: 32rpx;
      overflow-wrap: break-word;
      color: rgba(144, 146, 149, 1);
      font-size: 24rpx;
      font-family: PingFang SC-Regular;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 32rpx;
      margin-left: 2rpx;
    }

    .thumbnail_4 {
      width: 32rpx;
      height: 32rpx;
      margin-left: 30rpx;
    }

    .text_13 {
      width: 40rpx;
      height: 32rpx;
      overflow-wrap: break-word;
      color: rgba(144, 146, 149, 1);
      font-size: 24rpx;
      font-family: PingFang SC-Regular;
      font-weight: normal;
      text-align: left;
      white-space: nowrap;
      line-height: 32rpx;
      margin-left: 2rpx;
    }
  }

  .box_3 {
    background-color: rgba(225, 228, 233, 1);
    width: 636rpx;
    height: 2rpx;
    margin: 30rpx 32rpx 0 32rpx;
  }
}

.no-more-data {
  background: white;
  padding: 8rpx;
  text-align: center;

  .no-more-text {
    color: rgba(147, 152, 160, 1);
    font-size: 22rpx;
    font-family: PingFang SC-Regular;
    font-weight: normal;
  }
}

.result-summary {
  width: 700rpx;
  margin: 0 auto 30rpx auto;
  padding: 20rpx 30rpx;
  background: rgba(30, 144, 255, 0.1);
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #1E90FF;
  font-weight: 600;
  text-align: center;
  box-sizing: border-box;
}

.result-section {
  width: 700rpx;
  margin: 0 auto 50rpx auto;
  background: white;
  border-radius: 0 0 12rpx 12rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.08);

  .block_3 {
    border-radius: 12rpx 12rpx 0 0;
  }
}

.section-title {
  display: flex;
  align-items: center;
  gap: 15rpx;
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  padding: 20rpx 30rpx;
  background: linear-gradient(135deg, rgba(30, 144, 255, 0.1), rgba(74, 144, 226, 0.1));
  border-radius: 16rpx;
  border-left: 6rpx solid #1E90FF;
  
  .icon {
    font-size: 38rpx;
  }
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.result-item {
  background: white;
  padding: 35rpx;
  border-radius: 20rpx;
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border-left: 4rpx solid transparent;
  
  &:active {
    transform: scale(0.98);
    border-left-color: #1E90FF;
    box-shadow: 0 8rpx 40rpx rgba(30, 144, 255, 0.15);
  }
}



.meta-right {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.category {
  background: linear-gradient(135deg, #1E90FF, #4A90E2);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.date {
  color: #999;
  font-size: 26rpx;
}

.no-results {
  width: 700rpx;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
  text-align: center;
}

.no-results-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.no-results-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
}

.no-results-tip {
  font-size: 26rpx;
  color: #999;
}

.load-more-section {
  width: 700rpx;
  margin: 20rpx auto 0 auto;
}

.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  background: linear-gradient(135deg, #1E90FF, #4A90E2);
  color: white;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 2rpx 10rpx rgba(30, 144, 255, 0.5);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 10rpx rgba(30, 144, 255, 0.5);
  }
}

.load-more-icon {
  font-size: 24rpx;
  animation: bounce 2s infinite;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  background: rgba(255, 255, 255, 0.9);
  color: #666;
  border-radius: 50rpx;
  font-size: 28rpx;
}

.loading-more-icon {
  animation: spin 1s linear infinite;
}

.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  background: rgba(255, 255, 255, 0.9);
  color: #999;
  border-radius: 50rpx;
  font-size: 26rpx;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-6rpx);
  }
  60% {
    transform: translateY(-3rpx);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}



.item-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  line-height: 1.4;

  /* 标题最多显示2行 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 89rpx; /* 32rpx * 1.4 * 2 ≈ 89rpx */
}

.item-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;

  /* 内容最多显示3行 */
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 134rpx; /* 28rpx * 1.6 * 3 ≈ 134rpx */
}

.item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meta-right {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.category {
  background: linear-gradient(135deg, #1E90FF, #4A90E2);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.date {
  color: #999;
  font-size: 26rpx;
}

.loading-state {
  width: 700rpx;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  text-align: center;
}

.loading-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.loading-text {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
}

.load-more-text {
  font-size: 28rpx;
  font-weight: 600;
}

/* 政策解读列表样式 */
.list-items_1 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 0;
  width: 700rpx;
  height: 224rpx;
  margin-bottom: 0;
  box-shadow: none;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 8rpx 40rpx rgba(30, 144, 255, 0.15);
  }

  &:first-child {
    border-radius: 0;
  }

  &:last-child {
    border-radius: 0 0 12rpx 12rpx;
  }

  .box_2 {
    width: 636rpx;
    height: 160rpx;
    margin: 32rpx 32rpx 0 32rpx;

    .image-wrapper_1 {
      border-radius: 8px;
      height: 160rpx;
      width: 212rpx;
      overflow: hidden;
      position: relative;

      .label_3 {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;

        &.placeholder-bg {
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
        }
      }

      .play-icon-small {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 40rpx;
        height: 40rpx;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 20rpx;
      }

      .text-icon-small {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 40rpx;
        height: 40rpx;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24rpx;
      }
    }

    .box_3 {
      width: 406rpx;
      height: 160rpx;
      margin-left: 18rpx;

      .section_3 {
        width: 406rpx;
        height: 48rpx;

        .section_4 {
          background-color: rgba(249, 249, 249, 1);
          height: 44rpx;
          margin-top: 2rpx;
          width: auto;
          min-width: 112rpx;

          .text-wrapper_3 {
            background-color: rgba(31, 115, 255, 0.1);
            border-radius: 4px;
            height: 44rpx;
            width: 100%;
            padding: 0 12rpx;

            .text_7 {
              width: 100%;
              height: 40rpx;
              overflow-wrap: break-word;
              color: rgba(31, 115, 255, 1);
              font-size: 24rpx;
              font-family: PingFang SC-Semibold;
              font-weight: 600;
              text-align: center;
              white-space: nowrap;
              line-height: 40rpx;
              margin: 2rpx 0 0 0;
            }
          }
        }

        .text_8 {
          flex: 1;
          height: 48rpx;
          overflow-wrap: break-word;
          color: rgba(26, 32, 44, 1);
          font-size: 32rpx;
          font-family: PingFang SC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          line-height: 48rpx;
          margin-left: 16rpx;
        }
      }

      .text_9 {
        width: 406rpx;
        height: 32rpx;
        overflow-wrap: break-word;
        color: rgba(158, 158, 158, 1);
        font-size: 24rpx;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        line-height: 32rpx;
        margin-top: 8rpx;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .section_5 {
        width: 282rpx;
        height: 32rpx;
        margin: 24rpx 0 0 0;

        .thumbnail_2 {
          width: 32rpx;
          height: 32rpx;
        }

        .text_10 {
          width: 40rpx;
          height: 32rpx;
          overflow-wrap: break-word;
          color: rgba(144, 146, 149, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 32rpx;
          margin-left: 2rpx;
        }

        .thumbnail_3 {
          width: 32rpx;
          height: 32rpx;
          margin-left: 30rpx;
        }

        .text_11 {
          width: 40rpx;
          height: 32rpx;
          overflow-wrap: break-word;
          color: rgba(144, 146, 149, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 32rpx;
          margin-left: 2rpx;
        }

        .thumbnail_4 {
          width: 32rpx;
          height: 32rpx;
          margin-left: 30rpx;
        }

        .text_12 {
          width: 40rpx;
          height: 32rpx;
          overflow-wrap: break-word;
          color: rgba(144, 146, 149, 1);
          font-size: 24rpx;
          font-family: PingFang SC-Regular;
          font-weight: normal;
          text-align: left;
          white-space: nowrap;
          line-height: 32rpx;
          margin-left: 2rpx;
        }
      }
    }
  }

  .box_4 {
    background-color: rgba(225, 228, 233, 1);
    width: 636rpx;
    height: 2rpx;
    margin: 30rpx 32rpx 0 32rpx;
  }
}

/* 渐变色背景占位符 */
.interpretation-bg-1 { background: linear-gradient(135deg, #FF9A8B 0%, #FF6A88 100%); }
.interpretation-bg-2 { background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%); }
.interpretation-bg-3 { background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%); }
.interpretation-bg-4 { background: linear-gradient(135deg, #fccb90 0%, #d57eeb 100%); }
.interpretation-bg-5 { background: linear-gradient(135deg, #5ee7df 0%, #b490ca 100%); }
.interpretation-bg-6 { background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); }
</style> 