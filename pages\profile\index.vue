<template>
	<view class="page flex-col">
		<view class="box_1 flex-col">
			<!-- 登录/用户信息区域 -->
			<view v-if="!isLoggedIn" class="box_3 flex-row justify-between">
				<view class="image-text_1 flex-row justify-between">
					<image class="image_3" referrerpolicy="no-referrer"
						src="/static/images/user-avatar-placeholder.png" />
					<view class="text-group_1 flex-col justify-between">
						<text class="text_2">登录体验更多服务</text>
						<text class="text_3">收藏、咨询、浏览记录一目了然</text>
					</view>
				</view>
				<view class="text-wrapper_1 flex-col" @click="handleLogin">
					<text class="text_4">微信一键登录</text>
				</view>
			</view>

			<!-- 已登录用户信息区域 -->
			<view v-else class="box_3 flex-row justify-between">
				<view class="image-text_1 flex-row justify-between">
					<image class="image_3" referrerpolicy="no-referrer"
						:src="userInfo.avatar || '/static/images/default-avatar.png'" />
					<view class="text-group_1 flex-col justify-between">
						<text class="text_2">{{ userInfo.nickname || '微信用户' }}</text>
						<text class="text_3">渝汇万家服务用户</text>
					</view>
				</view>
				<view class="text-wrapper_1 flex-col" @click="logout">
					<text class="text_4">退出登录</text>
				</view>
			</view>

			<!-- 统计数据卡片 -->
			<view class="box_4 flex-col">
				<view class="text-wrapper_2 flex-row">
					<text class="text_5" @click="toCollectionPage">{{ userStats.collectionCount || 0 }}</text>
					<text class="text_6" @click="toInquiryPage">{{ userStats.inquiryCount || 0 }}</text>
					<text class="text_7" @click="toHistoryPage">{{ userStats.viewCount || 0 }}</text>
				</view>
				<view class="text-wrapper_3 flex-row">
					<text class="text_8" @click="toCollectionPage">我的收藏</text>
					<text class="text_9" @click="toInquiryPage">我的咨询</text>
					<text class="text_10" @click="toHistoryPage">浏览历史</text>
				</view>
			</view>
		</view>

		<!-- 联系我们 -->
		<view class="box_5 flex-col">
			<view class="group_1 flex-row justify-between">
				<view class="group_2 flex-col"></view>
				<text class="text_11">联系我们</text>
			</view>
			<view class="list_1 flex-col">
				<view class="list-items_1 flex-row" :style="{ background: item.lanhuBg0 }" v-for="(item, index) in serviceData" :key="index"
					@click="openService(item.type)">
					<image class="label_1" referrerpolicy="no-referrer" :src="item.icon" />
					<view class="text-group_2 flex-col justify-between">
						<text class="text_12">{{ item.title }}</text>
						<text class="text_13">{{ item.desc }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 设置选项 -->
		<view class="box_6 flex-col">
			<view class="list_2 flex-col">
				<view class="list-items_2 flex-row justify-between" v-for="(item, index) in settingsData" :key="index"
					@click="handleSettingsClick(item.type)">
					<view class="image-text_2 flex-row justify-between">
						<image class="label_2" referrerpolicy="no-referrer" :src="item.icon" />
						<text class="text-group_3">{{ item.title }}</text>
					</view>
					<image class="thumbnail_1" referrerpolicy="no-referrer" src="/static/images/arrow-right.png" />
				</view>
			</view>
		</view>


		<!-- 快速反馈弹窗 -->
		<view class="modal-overlay" v-if="showFeedbackModal" @click="showFeedbackModal = false">
			<view class="feedback-modal" catchtap="true">
				<view class="modal-header">
					<text class="modal-title">意见反馈</text>
					<text class="modal-close" @click="showFeedbackModal = false">×</text>
				</view>
				<view class="modal-content">
					<textarea class="feedback-textarea" v-model="feedbackContent" placeholder="请描述您遇到的问题或建议..."
						maxlength="500"></textarea>
					<view class="char-count">{{ feedbackContent.length }}/500</view>
				</view>
				<view class="modal-footer">
					<view class="modal-btn cancel-btn" @click="showFeedbackModal = false">取消</view>
					<view class="modal-btn submit-btn" @click="submitFeedback">提交</view>
				</view>
			</view>
		</view>

		<!-- 自定义TabBar -->
		<custom-tab-bar />
	</view>
</template>

<script>
	import {
		api
	} from '@/utils/api'
	import CustomTabBar from '@/custom-tab-bar/index.vue'

	export default {
		components: {
			CustomTabBar
		},
		data() {
			return {
				isLoggedIn: false,
				userInfo: {
					nickname: '',
					avatar: ''
				},
				userStats: {
					collectionCount: 0,
					inquiryCount: 0,
					viewCount: 0
				},
				showFeedbackModal: false,
				feedbackContent: '',

				// 联系我们数据
				serviceData: [{
						type: 'hotline',
						title: '外汇局咨询热线',
						desc: '023-67677161',
						lanhuBg0: 'linear-gradient(270deg, rgba(255,243,211,1.000000) 0, rgba(255,243,211,0.300000) 100.000000%)',
						icon: '/static/images/phone-icon.png'
					},
					{
						type: 'location',
						title: '银行网点一览',
						desc: '就近银行机构查询',
						lanhuBg0: 'linear-gradient(270deg, rgba(235,236,255,1.000000) 0, rgba(235,236,255,0.300000) 100.000000%)',
						icon: '/static/images/location-icon.png'
					}
				],

				// 设置选项数据
				settingsData: [{
						type: 'clearCache',
						title: '清除缓存',
						icon: '/static/images/cache-icon.png'
					},
					{
						type: 'about',
						title: '关于我们',
						icon: '/static/images/about-icon.png'
					}
				]
			}
		},
		onLoad() {
			// this.loadUserInfo()
			// this.loadUserStats()
		},
		onShow() {
			if (typeof this.$root.$mp.page.getTabBar === 'function' && this.$root.$mp.page.getTabBar()) {
				this.$root.$mp.page.getTabBar().$vm.updateSelected(3)
			}
			this.checkLoginStatus();
		},
		methods: {
			checkLoginStatus() {
				const token = uni.getStorageSync('token');
				if (token) {
					this.isLoggedIn = true;
					this.loadUserInfo();
					this.loadUserStats();
				} else {
					this.isLoggedIn = false;
					// 清空用户信息和统计数据
					this.userInfo = {
						nickname: '微信用户',
						avatar: '/static/images/default-avatar.png'
					};
					this.userStats = {
						collectionCount: 0,
						inquiryCount: 0,
						viewCount: 0
					};
				}
			},

			handleLogin() {
				uni.getUserProfile({
					desc: '用于完善会员资料',
					success: (userProfileRes) => {
						const userProfile = {
							nickname: userProfileRes.userInfo.nickName,
							avatar: userProfileRes.userInfo.avatarUrl,
						};
						this.userInfo = userProfile; // 立即更新UI

						uni.login({
							provider: 'weixin',
							success: async (loginRes) => {
								const params = {
									code: loginRes.code,
									nickname: userProfile.nickname,
									avatar: userProfile.avatar,
								};
								try {
									uni.showLoading({
										title: '登录中...'
									});
									const res = await api.wechatLogin(params);
									if (res.code === 200 && res.data.token) {
										uni.setStorageSync('token', res.data.user.id);

										// 合并后端返回的用户信息（如ID）和从微信获取的最新信息
										const finalUserInfo = {
											...res.data.user,
											...userProfile
										};

										uni.setStorageSync('userInfo', finalUserInfo);
										this.isLoggedIn = true;
										this.userInfo = finalUserInfo; // 再次更新，确保数据完整
										this.loadUserStats();
										uni.hideLoading();
										uni.showToast({
											title: '登录成功',
											icon: 'success'
										});
									} else {
										throw new Error('登录失败，请重试');
									}
								} catch (error) {
									uni.hideLoading();
									uni.showToast({
										title: error.message || '登录时发生错误',
										icon: 'none',
									});
								}
							},
							fail: (err) => {
								uni.showToast({
									title: '微信登录授权失败',
									icon: 'none'
								});
							}
						});
					},
					fail: (err) => {
						uni.showToast({
							title: '您取消了授权',
							icon: 'none'
						});
					},
				});
			},

			loadUserInfo() {
				const userInfo = uni.getStorageSync('userInfo');
				if (userInfo) {
					this.userInfo = userInfo;
				}
			},

			async loadUserStats() {
				if (!this.isLoggedIn) return;

				const user = uni.getStorageSync('userInfo');
				if (!user || !user.id) {
					console.error("无法获取用户信息或用户ID");
					return;
				}

				uni.showLoading({
					title: '加载中...',
					mask: true
				});
				try {
					const [collectionRes, inquiryRes, viewRes] = await Promise.all([
						api.getMyCollections({
							per_page: 1
						}),
						api.getMyInquiries(user.id, {
							per_page: 1
						}),
						api.getMyInteractions({
							action: 'view',
							per_page: 1
						})
					]);

					this.userStats = {
						collectionCount: collectionRes.data.total || 0,
						inquiryCount: inquiryRes.data.total || 0,
						viewCount: viewRes.data.total || 0,
					};
				} catch (error) {
					console.error('加载用户统计失败:', error);
					uni.showToast({
						title: '统计数据加载失败',
						icon: 'none'
					});
					// 保留旧数据或清零
					this.userStats = {
						collectionCount: 'N/A',
						inquiryCount: 'N/A',
						viewCount: 'N/A'
					};
				} finally {
					uni.hideLoading();
				}
			},

			toCollectionPage() {
				if (!this.isLoggedIn) {
					this.promptLogin('查看收藏');
					return;
				}
				uni.navigateTo({
					url: '/pages/profile/collection'
				})
			},

			toInquiryPage() {
				if (!this.isLoggedIn) {
					this.promptLogin('查看咨询');
					return;
				}
				uni.navigateTo({
					url: '/pages/profile/inquiry'
				})
			},

			toHistoryPage() {
				if (!this.isLoggedIn) {
					this.promptLogin('查看浏览历史');
					return;
				}
				uni.navigateTo({
					url: '/pages/profile/history'
				})
			},

			promptLogin(action) {
				uni.showModal({
					title: '请先登录',
					content: `登录后才能${action}哦`,
					confirmText: '去登录',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							// 用户点击确认，可以在此处理登录逻辑，或者停留在当前页让用户点击登录按钮
							// 当前逻辑是让用户停留在个人中心页手动登录
						}
					}
				});
			},

			toFeedbackPage() {
				this.showFeedbackModal = true
			},

			openService(type) {
				switch (type) {
					case 'hotline':
						uni.makePhoneCall({
							phoneNumber: '023-********'
						})
						break
					case 'location':
						uni.navigateTo({
							url: '/pages/bank/list'
						})
						break
					case 'policy':
						uni.switchTab({
							url: '/pages/policy/index'
						})
						break
				}
			},

			clearCache() {
				uni.showModal({
					title: '清除缓存',
					content: '确认清除所有缓存数据吗？',
					success: (res) => {
						if (res.confirm) {
							// 清除缓存逻辑
							uni.clearStorageSync()
							this.isLoggedIn = false;
							this.checkLoginStatus(); // 重新检查状态并更新UI
							uni.showToast({
								title: '缓存已清除',
								icon: 'success'
							})
						}
					}
				})
			},

			aboutApp() {
				uni.showModal({
					title: '关于我们',
					content: '重庆跨境融资服务小程序\n\n为企业提供专业的跨境融资政策咨询服务，助力重庆开放型经济发展。\n\n联系我们：023-********',
					showCancel: false
				})
			},

			// 处理设置选项点击
			handleSettingsClick(type) {
				switch (type) {
					case 'clearCache':
						this.clearCache()
						break
					case 'about':
						this.aboutApp()
						break
				}
			},

			// 退出登录
			logout() {
				uni.showModal({
					title: '退出登录',
					content: '确认退出登录吗？',
					success: (res) => {
						if (res.confirm) {
							uni.clearStorageSync()
							this.isLoggedIn = false
							this.checkLoginStatus()
							uni.showToast({
								title: '已退出登录',
								icon: 'success'
							})
						}
					}
				})
			},

			async submitFeedback() {
				if (!this.feedbackContent.trim()) {
					uni.showToast({
						title: '请输入反馈内容',
						icon: 'none'
					})
					return
				}

				try {
					uni.showLoading({
						title: '提交中...'
					})

					// 提交反馈接口
					// await api.submitFeedback({ content: this.feedbackContent })

					uni.hideLoading()
					uni.showToast({
						title: '提交成功',
						icon: 'success'
					})

					this.showFeedbackModal = false
					this.feedbackContent = ''

				} catch (error) {
					uni.hideLoading()
					console.error('提交反馈失败:', error)
					uni.showToast({
						title: '提交失败，请重试',
						icon: 'none'
					})
				}
			}
		}
	}
</script>

<style lang="scss" scoped>

.page {
  background-color: rgba(242, 245, 247, 1);
  position: relative;
  width: 750rpx;
  height: 1624rpx;
  overflow: hidden;
  .box_1 {
    position: relative;
    width: 750rpx;
    height: 316rpx;
    background: linear-gradient(180deg,
      #1E7DFA 0%,     /* 顶部标题颜色起始 */
      #188DF5 20%,    /* 顶部标题颜色结束 */
      #58B1F5 60%,    /* 搜索区域颜色 */
      #F2F4F5 100%    /* 分类tab区域颜色 */
    ) 100% no-repeat;
    background-size: 100% 100%;
    .image_1 {
      width: 750rpx;
      height: 88rpx;
    }
    .box_2 {
      background-color: rgba(0, 0, 0, 1);
      width: 750rpx;
      height: 88rpx;
      .text_1 {
        width: 144rpx;
        height: 88rpx;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 36rpx;
        font-family: PingFang SC-Semibold;
        font-weight: 600;
        text-align: center;
        line-height: 88rpx;
        margin-left: 304rpx;
      }
      .image_2 {
        width: 168rpx;
        height: 64rpx;
        margin: 12rpx 16rpx 0 118rpx;
      }
    }
    .box_3 {
      width: 718rpx;
      height: 112rpx;
      margin: 48rpx 0 180rpx 32rpx;
      .image-text_1 {
        width: 470rpx;
        height: 112rpx;
        .image_3 {
          width: 112rpx;
          height: 112rpx;
        }
        .text-group_1 {
          width: 336rpx;
          height: 100rpx;
          margin-top: 4rpx;
          .text_2 {
            width: 288rpx;
            height: 52rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 36rpx;
            font-family: PingFang SC-Semibold;
            font-weight: 600;
            text-align: center;
            white-space: nowrap;
            line-height: 50rpx;
            margin-left: 2rpx;
          }
          .text_3 {
            width: 336rpx;
            height: 34rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 24rpx;
            font-family: PingFang SC-Regular;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 32rpx;
            margin-top: 14rpx;
          }
        }
      }
      .text-wrapper_1 {
        box-shadow: 0px 4px 4px 0px rgba(90, 205, 248, 1);
        background-color: rgba(255, 255, 255, 1);
        height: 88rpx;
        margin-top: 12rpx;
        width: 208rpx;
        .text_4 {
          width: 168rpx;
          height: 32rpx;
          overflow-wrap: break-word;
          color: rgba(31, 115, 255, 1);
          font-size: 28rpx;
          font-family: PingFang SC-Semibold;
          font-weight: 600;
          text-align: left;
          white-space: nowrap;
          line-height: 32rpx;
          margin: 28rpx 0 0 22rpx;
        }
      }
    }
    .box_4 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 12px;
      position: absolute;
      left: 32rpx;
      top: 196rpx;
      width: 686rpx;
      height: 192rpx;
      .text-wrapper_2 {
        width: 686rpx;
        height: 100rpx;
        background: url(/static/images/stats-bg.png) 100% no-repeat;
        background-size: 100% 100%;
        justify-content: flex-center;
        .text_5 {
          width: 222rpx;
          height: 56rpx;
          overflow-wrap: break-word;
          color: rgba(22, 23, 26, 1);
          font-size: 40rpx;
          font-family: PingFang SC-Semibold;
          font-weight: 600;
          text-align: center;
          white-space: nowrap;
          line-height: 56rpx;
          margin: 40rpx 0 0 14rpx;
        }
        .text_6 {
          width: 222rpx;
          height: 56rpx;
          overflow-wrap: break-word;
          color: rgba(22, 23, 26, 1);
          font-size: 40rpx;
          font-family: PingFang SC-Semibold;
          font-weight: 600;
          text-align: center;
          white-space: nowrap;
          line-height: 56rpx;
          margin: 40rpx 0 0 -4rpx;
        }
        .text_7 {
          width: 222rpx;
          height: 56rpx;
          overflow-wrap: break-word;
          color: rgba(22, 23, 26, 1);
          font-size: 40rpx;
          font-family: PingFang SC-Semibold;
          font-weight: 600;
          text-align: center;
          white-space: nowrap;
          line-height: 56rpx;
          margin: 40rpx 14rpx 0 -4rpx;
        }
      }
      .text-wrapper_3 {
        width: 658rpx;
        height: 40rpx;
        margin: 12rpx 0 40rpx 14rpx;
        .text_8 {
          width: 222rpx;
          height: 40rpx;
          overflow-wrap: break-word;
          color: rgba(150, 156, 164, 1);
          font-size: 28rpx;
          font-family: PingFang SC-Regular;
          font-weight: normal;
          text-align: center;
          white-space: nowrap;
          line-height: 38rpx;
        }
        .text_9 {
          width: 222rpx;
          height: 40rpx;
          overflow-wrap: break-word;
          color: rgba(150, 156, 164, 1);
          font-size: 28rpx;
          font-family: PingFang SC-Regular;
          font-weight: normal;
          text-align: center;
          white-space: nowrap;
          line-height: 38rpx;
          margin-left: -4rpx;
        }
        .text_10 {
          width: 222rpx;
          height: 40rpx;
          overflow-wrap: break-word;
          color: rgba(150, 156, 164, 1);
          font-size: 28rpx;
          font-family: PingFang SC-Regular;
          font-weight: normal;
          text-align: center;
          white-space: nowrap;
          line-height: 38rpx;
          margin-left: -4rpx;
        }
      }
    }
  }
  .box_5 {
    background-color: rgba(255, 255, 255, 1);
    border-radius: 12px;
    width: 686rpx;
    height: 448rpx;
    margin: 88rpx 0 0 32rpx;
    .group_1 {
      width: 150rpx;
      height: 88rpx;
      margin-left: 32rpx;
      .group_2 {
        background-color: rgba(31, 115, 255, 1);
        width: 8rpx;
        height: 32rpx;
        margin-top: 28rpx;
      }
      .text_11 {
        width: 128rpx;
        height: 88rpx;
        overflow-wrap: break-word;
        color: rgba(26, 32, 44, 1);
        font-size: 32rpx;
        font-family: PingFang SC-Semibold;
        font-weight: 600;
        text-align: left;
        line-height: 88rpx;
      }
    }
    .list_1 {
      width: 622rpx;
      height: 328rpx;
      justify-content: space-between;
      margin: 0 0 32rpx 32rpx;
      .list-items_1 {
        background-image: linear-gradient(
          270deg,
          rgba(255, 243, 211, 1) 0,
          rgba(255, 243, 211, 0.3) 100%
        );
        border-radius: 8px;
        width: 622rpx;
        height: 152rpx;
        margin-bottom: 24rpx;
        .label_1 {
          width: 64rpx;
          height: 64rpx;
          margin: 44rpx 0 0 48rpx;
        }
        .text-group_2 {
          width: 196rpx;
          height: 88rpx;
          margin: 32rpx 268rpx 0 46rpx;
          .text_12 {
            width: 196rpx;
            height: 40rpx;
            overflow-wrap: break-word;
            color: rgba(26, 32, 44, 1);
            font-size: 28rpx;
            font-family: PingFang SC-Semibold;
            font-weight: 600;
            text-align: left;
            white-space: nowrap;
            line-height: 38rpx;
          }
          .text_13 {
            width: 192rpx;
            height: 32rpx;
            overflow-wrap: break-word;
            color: rgba(147, 152, 160, 1);
            font-size: 24rpx;
            font-family: PingFang SC-Regular;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 32rpx;
            margin-top: 16rpx;
          }
        }
      }
    }
  }
  .box_6 {
    width: 750rpx;
    height: 408rpx;
    margin-bottom: 164rpx;
    .list_2 {
      width: 686rpx;
      height: 216rpx;
      justify-content: space-between;
      margin: 24rpx 0 0 32rpx;
      .list-items_2 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 12px;
        width: 686rpx;
        height: 96rpx;
        margin-bottom: 24rpx;
        .image-text_2 {
          width: 184rpx;
          height: 96rpx;
          margin-left: 32rpx;
          .label_2 {
            width: 48rpx;
            height: 48rpx;
            margin-top: 24rpx;
          }
          .text-group_3 {
            width: 112rpx;
            height: 96rpx;
            overflow-wrap: break-word;
            color: rgba(26, 32, 44, 1);
            font-size: 28rpx;
            font-family: PingFang SC-Regular;
            font-weight: normal;
            text-align: left;
            line-height: 96rpx;
          }
        }
        .thumbnail_1 {
          width: 32rpx;
          height: 32rpx;
          margin: 32rpx 32rpx 0 0;
        }
      }
    }
  }
  .box_7 {
    background-color: rgba(255, 255, 255, 1);
    position: absolute;
    left: 0;
    top: 1458rpx;
    width: 750rpx;
    height: 166rpx;
    .image_4 {
      width: 750rpx;
      height: 2rpx;
    }
    .list_3 {
      width: 740rpx;
      height: 80rpx;
      margin: 14rpx 0 0 4rpx;
      .image-text_3 {
        width: 188rpx;
        height: 80rpx;
        margin-right: -4rpx;
        .label_3 {
          width: 48rpx;
          height: 48rpx;
          margin-left: 70rpx;
        }
        .text-group_4 {
          width: 188rpx;
          height: 28rpx;
          overflow-wrap: break-word;
          font-size: 20rpx;
          font-family: PingFang SC-Regular;
          font-weight: normal;
          text-align: center;
          white-space: nowrap;
          line-height: 28rpx;
          margin-top: 4rpx;
        }
      }
    }
    .image_5 {
      width: 750rpx;
      height: 68rpx;
      margin-top: 2rpx;
    }
  }
}
</style>