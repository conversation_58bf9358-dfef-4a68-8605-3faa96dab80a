<template>
  <view class="policy-page">
    <!-- 搜索栏 -->
    <view class="group_2 flex-row">
      <view class="image-text_1 flex-row justify-between" @click="toSearch">
        <image
          class="thumbnail_1"
          src="/static/images/search-icon.png"
        />
        <text class="text-group_1">搜索政策文件...</text>
      </view>
    </view>

    <!-- 分类筛选 -->
    <view class="text-wrapper_1">
      <scroll-view class="tab-scroll" scroll-x :scroll-into-view="scrollIntoView">
        <view class="tab-list flex-row">
          <view
            v-for="(item, index) in categoryList"
            :key="item.value"
            :id="`tab-${index}`"
            :class="getTabClass(item.value, index)"
            @click="selectCategory(item.value, index)"
            class="tab-item-wrapper"
          >
            <text class="tab-text">{{ item.label }}</text>
            <view class="tab-indicator" v-if="currentCategory === item.value"></view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 政策列表 -->
    <!-- 空状态 -->
    <view class="empty-container flex-col" v-if="policyList.length === 0 && !loading">
      <view class="empty-content flex-row">
        <view class="empty-image-text flex-col justify-between">
          <image
            class="empty-image"
            src="/static/images/empty-state.png"
          />
          <view class="empty-text-group flex-col justify-between">
            <text class="empty-title">没有找到相关内容</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 政策列表 -->
    <view class="box_2 flex-col" v-else>
      <view class="list_1 flex-col">
        <view
          class="list-items_1 flex-col"
          v-for="(policy, index) in policyList"
          :key="policy.id"
          @click="toPolicyDetail(policy.id)"
        >
          <text class="text_6">{{ policy.title }}</text>
          <text class="text_7">{{ stripHtmlTags(policy.content) || '促进西部陆海新通道与中欧班列、长江黄金水道高效联动...' }}</text>
          <view class="box_3 flex-row">
            <text class="text_8">{{ formatDate(policy.publish_date) }}</text>
            <image
              class="thumbnail_2"
              src="/static/images/date-icon.png"
            />
            <text class="text_9">{{ policy.view_count || 0 }}</text>
            <image
              class="thumbnail_3"
              src="/static/images/view-icon.png"
            />
            <text class="text_10">{{ policy.like_count || 0 }}</text>
            <image
              class="thumbnail_4"
              src="/static/images/like-icon.png"
            />
            <text class="text_11">{{ policy.collect_count || 0 }}</text>
            <image
              class="thumbnail_5"
              src="/static/images/share-icon.png"
            />
            <text class="text_12">{{ policy.forward_count || 0 }}</text>
          </view>
        </view>
      </view>
      <text class="text_13" v-if="!hasMore && policyList.length > 0">没有更多数据了</text>
    </view>




    
    <!-- 自定义TabBar -->
    <custom-tab-bar />
  </view>
</template>

<script>
import { api } from '@/utils/api'
import CustomTabBar from '@/custom-tab-bar/index.vue'

export default {
  components: {
    CustomTabBar
  },
  data() {
    return {
      currentCategory: '',
      categoryList: [
        { label: '全部', value: '' },
        { label: '外汇管理', value: '外汇管理' },
        { label: '跨境融资', value: '跨境融资' },
        { label: '地方政策', value: '地方政策' },
        { label: '金融监管', value: '金融监管' },
        { label: '对外投资', value: '对外投资' },
        { label: '人民币国际化', value: '人民币国际化' }
      ],
      policyList: [],
      currentPage: 1,
      hasMore: true,
      loading: false,
      scrollIntoView: ''
    }
  },
  onLoad() {
    this.loadPolicyList()
  },
  onPullDownRefresh() {
    this.refreshData()
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore()
    }
  },
  onShow() {
    if (typeof this?.$root?.$mp?.page?.getTabBar === 'function' && this.$root.$mp.page.getTabBar()) {
      this.$root.$mp.page.getTabBar().$vm.updateSelected(1);
    }
  },
  methods: {
    // 处理富文本，去除HTML标签
    stripHtmlTags(html) {
      if (!html) return '';
      // 去除HTML标签，保留纯文本
      return html.replace(/<[^>]*>/g, '')
                 .replace(/&nbsp;/g, ' ')
                 .replace(/&lt;/g, '<')
                 .replace(/&gt;/g, '>')
                 .replace(/&amp;/g, '&')
                 .replace(/&quot;/g, '"')
                 .trim();
    },

    // 获取tab样式类
    getTabClass(value, index) {
      const baseClasses = ['tab-item'];

      // 选中状态
      if (this.currentCategory === value) {
        baseClasses.push('active');
      }

      return baseClasses.join(' ');
    },

    async loadPolicyList(reset = false) {
      if (this.loading) return
      
      this.loading = true
      
      try {
        const params = {
          page: reset ? 1 : this.currentPage,
          per_page: 20
        }
        
        if (this.currentCategory) {
          params.category1 = this.currentCategory
        }
        
        const res = await api.getPolicies(params)
        const newList = res.data?.items || []
        
        if (reset) {
          this.policyList = newList
          this.currentPage = 1
        } else {
          this.policyList = this.policyList.concat(newList)
        }
        
        this.hasMore = newList.length === 20
        this.currentPage += 1
        
      } catch (error) {
        console.error('加载政策列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'none'
        })
        
        // 使用模拟数据
        if (reset || this.policyList.length === 0) {
          this.loadMockData()
        }
      } finally {
        this.loading = false
        if (reset) {
          uni.stopPullDownRefresh()
        }
      }
    },

    loadMockData() {
      this.policyList = [
        {
          id: 1,
          title: '国家外汇管理局关于进一步促进跨境贸易投资便利化的通知',
          category1: '外汇管理',
          category2: '跨境贸易',
          category3: '便民措施',
          content: '为深入贯彻党中央、国务院关于稳外贸稳外资的决策部署...',
          view_count: 312,
          publish_date: '2024-01-10'
        },
        {
          id: 2,
          title: '重庆市促进跨境融资发展实施细则',
          category1: '地方政策',
          category2: '跨境融资',
          category3: '执行细则',
          content: '根据国家相关政策，结合重庆实际，制定本实施细则...',
          view_count: 256,
          publish_date: '2024-01-12'
        },
        {
          id: 3,
          title: '银行业金融机构外汇业务管理办法',
          category1: '金融监管',
          category2: '银行业务',
          category3: '管理办法',
          content: '为规范银行业金融机构外汇业务经营行为...',
          view_count: 198,
          publish_date: '2024-01-18'
        },
        {
          id: 4,
          title: '企业对外投资备案管理办法',
          category1: '对外投资',
          category2: '备案管理',
          category3: '管理流程',
          content: '为加强和规范企业对外投资备案管理...',
          view_count: 234,
          publish_date: '2024-01-22'
        },
        {
          id: 5,
          title: '跨境人民币业务管理暂行办法',
          category1: '人民币国际化',
          category2: '跨境结算',
          category3: '暂行办法',
          content: '为促进跨境人民币业务健康发展...',
          view_count: 287,
          publish_date: '2024-01-28'
        }
      ]
    },

    selectCategory(category, index) {
      this.currentCategory = category
      this.handleTabScroll(index)
      this.refreshData()
    },

    // 智能滚动处理
    handleTabScroll(selectedIndex) {
      const totalTabs = this.categoryList.length

      // 如果tab总数较少，不需要滚动
      if (totalTabs <= 4) {
        return
      }

      let targetIndex = selectedIndex

      // 点击最左侧的tab时，如果前面还有tab，向左滚动一个位置
      if (selectedIndex === 0) {
        targetIndex = 0
      }
      // 点击最右侧的tab时，向右滚动，让后面的tab显示出来
      else if (selectedIndex === totalTabs - 1) {
        // 滚动到让最后一个tab显示在屏幕右侧，前面的tab向左移出
        targetIndex = Math.max(0, selectedIndex - 2)
      }
      // 点击中间的tab时，保持当前tab在屏幕中央偏左的位置
      else {
        // 让选中的tab保持在屏幕左侧有边距的位置
        targetIndex = Math.max(0, selectedIndex - 1)
      }

      this.scrollIntoView = `tab-${targetIndex}`

      // 延迟清空，确保滚动完成
      setTimeout(() => {
        this.scrollIntoView = ''
      }, 350)
    },

    refreshData() {
      this.loadPolicyList(true)
    },

    loadMore() {
      this.loadPolicyList()
    },

    toPolicyDetail(id) {
      uni.navigateTo({
        url: `/pages/policy/detail?id=${id}`
      })
    },

    toSearch() {
      uni.navigateTo({
        url: '/pages/search/index?type=policy'
      })
    },

    formatDate(dateStr) {
      const date = new Date(dateStr)
      return date.toLocaleDateString()
    }
  }
}
</script>

<style lang="scss" scoped>
/* 通用flex类 */
.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}

.policy-page {
  min-height: 100vh;
  background-color: #f8f9fa; /* 列表区域的纯色背景 */
  padding-bottom: 140rpx; /* 为自定义tabBar留出空间 */
  position: relative;
}

/* 顶部渐变背景层 */
.policy-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 210rpx; /* 增加高度，包括分类tab和指示线 */
  background: linear-gradient(180deg,
    #1E7DFA 0%,     /* 顶部标题颜色起始 */
    #166FF8 20%,    /* 顶部标题颜色结束 */
    #298FFC 60%,    /* 搜索区域颜色 */
    #35A3FE 100%    /* 分类tab区域颜色 */
  );
  z-index: 1;
}

/* 搜索框样式 */
.group_2 {
  padding: 30rpx;
  background: transparent; /* 透明背景，显示渐变背景 */
  position: relative;
  z-index: 2; /* 确保显示在渐变背景之上 */
}

.image-text_1 {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  width: 100%;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.image-text_1:active {
  background: #e8e8e8;
}

.thumbnail_1 {
  width: 28rpx;
  height: 28rpx;
  margin-right: 20rpx;
}

.text-group_1 {
  color: #999;
  font-size: 28rpx;
  font-weight: normal;
  text-align: left;
  line-height: 28rpx;
  flex: 1;
}

/* 分类tab区域样式 */
.text-wrapper_1 {
  height: 88rpx;
  background: transparent; /* 透明背景，显示渐变背景 */
  position: relative;
  z-index: 2; /* 确保显示在渐变背景之上 */
  padding: 0 22rpx; /* 左右添加内边距 */
}

.tab-scroll {
  white-space: nowrap;
  width: 100%;
  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE */
}

.tab-list {
  display: flex;
  align-items: center;
  height: 88rpx;
}

.tab-item-wrapper {
  position: relative;
  margin-right: 32rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  white-space: nowrap;
  flex-shrink: 0; /* 防止tab被压缩 */
  z-index: 3; /* 确保tab容器在渐变背景之上 */
}

.tab-item-wrapper:first-child {
  margin-left: 0; /* 第一个tab左侧无边距，因为容器已有padding */
}

.tab-item-wrapper:last-child {
  margin-right: 0; /* 最后一个tab右侧无额外边距，容器padding已提供边距 */
}

.tab-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
  font-weight: normal;
  line-height: 1;
  transition: all 0.3s ease;
}

.tab-item.active .tab-text {
  color: rgba(255, 255, 255, 1);
  font-weight: 600;
}

.tab-indicator {
  position: absolute;
  bottom: 6rpx;
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% - 16rpx); /* 与文字宽度相近，留出少量边距 */
  min-width: 32rpx; /* 最小宽度 */
  max-width: 120rpx; /* 最大宽度限制 */
  height: 6rpx;
  background-color: #ffffff;
  border-radius: 3rpx;
  animation: slideIn 0.3s ease;
  z-index: 10; /* 确保指示线显示在最上层 */
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.3); /* 添加阴影增强可见性 */
}

@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: calc(100% - 16rpx);
    opacity: 1;
  }
}

/* 政策列表样式 */
.box_2 {
  width: 750rpx;
  margin-bottom: 164rpx;
}

.list_1 {
  width: 702rpx;
  justify-content: space-between;
  margin: 24rpx 0 0 24rpx;
}

.list-items_1 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12rpx;
  width: 702rpx;
  height: 224rpx;
  margin-bottom: 16rpx;
  justify-content: flex-start;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.list-items_1:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.text_6 {
  width: 544rpx;
  height: 48rpx;
  overflow-wrap: break-word;
  color: rgba(26, 32, 44, 1);
  font-size: 32rpx;
  font-weight: 600;
  text-align: left;
  white-space: nowrap;
  line-height: 48rpx;
  margin: 32rpx 0 0 30rpx;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text_7 {
  width: 642rpx;
  height: 48rpx;
  overflow-wrap: break-word;
  color: rgba(158, 158, 158, 1);
  font-size: 24rpx;
  font-weight: normal;
  text-align: left;
  line-height: 48rpx;
  margin: 8rpx 0 0 30rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.box_3 {
  width: 638rpx;
  height: 32rpx;
  margin: 24rpx 0 32rpx 30rpx;
  align-items: center;
}

.text_8 {
  width: 104rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgba(147, 152, 160, 1);
  font-size: 24rpx;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
}

.thumbnail_2 {
  width: 32rpx;
  height: 32rpx;
  margin-left: 148rpx;
}

.text_9 {
  width: 54rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgba(144, 146, 149, 1);
  font-size: 24rpx;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-left: 2rpx;
}

.thumbnail_3 {
  width: 32rpx;
  height: 32rpx;
  margin-left: 16rpx;
}

.text_10 {
  width: 40rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgba(144, 146, 149, 1);
  font-size: 24rpx;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-left: 2rpx;
}

.thumbnail_4 {
  width: 32rpx;
  height: 32rpx;
  margin-left: 30rpx;
}

.text_11 {
  width: 44rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgba(144, 146, 149, 1);
  font-size: 24rpx;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-left: 2rpx;
}

.thumbnail_5 {
  width: 32rpx;
  height: 32rpx;
  margin-left: 26rpx;
}

.text_12 {
  width: 40rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgba(144, 146, 149, 1);
  font-size: 24rpx;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 32rpx;
  margin-left: 2rpx;
}

.text_13 {
  width: 168rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(147, 152, 160, 1);
  font-size: 24rpx;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
  margin: 32rpx auto 0;
}



/* 空状态样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
}

.empty-content {
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.empty-image-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}

.empty-text-group {
  width: 240rpx;
  height: 90rpx;
  margin: 32rpx 0 0 2rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.empty-title {
  width: 224rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(26, 32, 44, 1);
  font-size: 28rpx;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 38rpx;
  margin-left: 8rpx;
}

.empty-subtitle {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(147, 152, 160, 1);
  font-size: 24rpx;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
  margin-top: 16rpx;
}
</style> 