<template>
  <view class="news-list-page">

    <!-- 筛选标签 -->
    <!-- <view class="filter-section" v-if="categories.length > 0">
      <scroll-view class="filter-scroll" scroll-x>
        <view class="filter-tags">
          <view 
            class="filter-tag" 
            :class="{ active: selectedCategory === '' }"
            @click="selectCategory('')"
          >
            全部
          </view>
          <view 
            v-for="category in categories" 
            :key="category"
            class="filter-tag" 
            :class="{ active: selectedCategory === category }"
            @click="selectCategory(category)"
          >
            {{ category }}
          </view>
        </view>
      </scroll-view>
    </view> -->

    <!-- 新闻列表 -->
    <view class="news-content">
      <view v-if="loading" class="loading-section">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载新闻资讯...</text>
      </view>

      <view v-else-if="filteredNewsList.length === 0" class="empty-section">
        <text class="empty-icon">🌉</text>
        <text class="empty-title">暂无相关新闻资讯</text>
        <text class="empty-subtitle">稍后再来看看</text>
      </view>

      <view v-else class="news-list">
        <view
          class="box_2 flex-row"
          v-for="news in filteredNewsList"
          :key="news.id"
          @click="toNewsDetail(news.id)"
        >
          <view class="group_1 flex-col justify-between">
            <text class="text_2">{{ news.title }}</text>
            <view class="section_2 flex-row">
              <view class="box_3 flex-col">
                <view class="text-wrapper_1 flex-col">
                  <text class="text_3">{{ news.category || '政策动态' }}</text>
                </view>
              </view>
              <image
                class="thumbnail_1"
                referrerpolicy="no-referrer"
                src="/static/images/date-icon-news.png"
              />
              <text class="text_4">{{ formatDate(news.publish_date) }}</text>
              <image
                class="thumbnail_2"
                referrerpolicy="no-referrer"
                src="/static/images/view-icon-news.png"
              />
              <text class="text_5">{{ formatViewCount(news.view_count) }}</text>
            </view>
          </view>
          <view class="group_2 flex-col">
            <image
              v-if="getNewsCover(news)"
              :src="getNewsCover(news)"
              class="news-cover-image"
              mode="aspectFill"
              @error="onNewsCoverError(news)"
            />
            <view
              v-else
              class="news-cover-placeholder"
              :class="'news-cover-' + getNewsCategoryType(news.category)"
            >
              {{ getNewsCategoryIcon(news.category) }}
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="filteredNewsList.length > 0" class="load-more">
        <view v-if="loading" class="loading-more">
          <view class="loading-spinner-small"></view>
          <text class="loading-more-text">正在加载...</text>
        </view>
        <view v-else-if="hasMore" class="load-more-btn" @click="loadMore">
          <text class="load-more-text">点击加载更多</text>
        </view>
        <view v-else class="no-more">
          <text class="no-more-text">没有更多内容了</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { api } from '@/utils/api.js'

export default {
  data() {
    return {
      newsList: [],
      filteredNewsList: [],
      loading: true,
      selectedCategory: '',
      categories: [],
      page: 1,
      perPage: 20,
      hasMore: true,

    }
  },

  onLoad() {
    this.loadNewsList()
  },

  onPullDownRefresh() {
    console.log('触发下拉刷新')
    this.refreshList()
  },

  onReachBottom() {
    console.log('触发上拉加载，hasMore:', this.hasMore, 'loading:', this.loading)
    if (this.hasMore && !this.loading) {
      this.loadMore()
    }
  },

  methods: {
    goBack() {
      uni.navigateBack()
    },

    async loadNewsList() {
      try {
        this.loading = true
        const params = {
          page: this.page,
          per_page: this.perPage
        }
        
        if (this.selectedCategory) {
          params.category = this.selectedCategory
        }
        


        const res = await api.getNews(params)
        console.log('新闻列表API返回:', res)
        
        if (res.success && res.data) {
          const news = res.data.items || []
          console.log(`第${this.page}页加载到${news.length}条数据`)

          if (this.page === 1) {
            this.newsList = news
          } else {
            this.newsList = [...this.newsList, ...news]
          }

          this.filteredNewsList = this.newsList
          this.hasMore = res.data.has_next || false

          console.log('总数据量:', this.newsList.length, '是否还有更多:', this.hasMore)

          // 提取分类
          if (this.page === 1) {
            this.extractCategories()
          }

          // 显示加载成功提示
          if (this.page === 1) {
            uni.showToast({
              title: '刷新成功',
              icon: 'success',
              duration: 1500
            })
          }
        } else {
          console.error('API返回数据格式错误:', res)
          uni.showToast({
            title: '数据格式错误',
            icon: 'error'
          })
        }
        

        
      } catch (error) {
        console.error('加载新闻列表失败:', error)
        uni.showToast({
          title: '加载失败',
          icon: 'error'
        })
      } finally {
        this.loading = false
        uni.stopPullDownRefresh()
      }
    },



    extractCategories() {
      const categories = [...new Set(this.newsList.map(news => news.category).filter(Boolean))]
      this.categories = categories
    },

    refreshList() {
      console.log('开始刷新列表')
      this.page = 1
      this.hasMore = true
      this.newsList = []
      this.filteredNewsList = []
      this.loadNewsList()
    },

    loadMore() {
      if (this.hasMore && !this.loading) {
        console.log('加载更多，当前页:', this.page)
        this.page++
        this.loadNewsList()
      } else {
        console.log('无法加载更多，hasMore:', this.hasMore, 'loading:', this.loading)
      }
    },



    selectCategory(category) {
      this.selectedCategory = category
      this.page = 1
      this.hasMore = true
      this.loadNewsList()
    },

    toNewsDetail(id) {
      uni.navigateTo({
        url: `/pages/news/detail?id=${id}`
      })
    },

    getNewsCover(news) {
      return news.cover_img || null
    },

    onNewsCoverError(news) {
      console.log('新闻封面加载失败:', news)
    },

    getNewsCategoryType(category) {
      const categoryMap = {
        '政策动态': 'policy',
        '产品创新': 'innovation',
        '风险防控': 'risk',
        '培训活动': 'training',
        '环境优化': 'environment',
        '数据报告': 'report'
      }
      return categoryMap[category] || 'default'
    },

    getNewsCategoryIcon(category) {
      const iconMap = {
        '政策动态': '📋',
        '产品创新': '💡',
        '风险防控': '🛡️',
        '培训活动': '🎓',
        '环境优化': '🌱',
        '数据报告': '📊'
      }
      return iconMap[category] || '📰'
    },

    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      const now = new Date()
      const diffTime = Math.abs(now - date)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      
      if (diffDays === 1) {
        return '昨天'
      } else if (diffDays <= 7) {
        return `${diffDays}天前`
      } else {
        return date.toLocaleDateString('zh-CN')
      }
    },

    formatViewCount(count) {
      if (!count) return '0'
      if (count >= 10000) {
        return (count / 10000).toFixed(1) + 'w'
      } else if (count >= 1000) {
        return (count / 1000).toFixed(1) + 'k'
      }
      return count.toString()
    }
  }
}
</script>

<style scoped>
.news-list-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.header-left {
  width: 80rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 48rpx;
  color: #333;
  font-weight: bold;
}

.header-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
}

.title-subtitle {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-top: 4rpx;
}

.header-right {
  width: 80rpx;
  display: flex;
  justify-content: flex-end;
}

.refresh-btn {
  font-size: 36rpx;
  color: #1E90FF;
}



.filter-section {
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-tags {
  display: flex;
  padding: 20rpx 30rpx;
  gap: 20rpx;
}

.filter-tag {
  padding: 12rpx 24rpx;
  background: #f8f8f8;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.filter-tag.active {
  background: #1E90FF;
  color: white;
}

.news-content {
  padding: 20rpx 0;
}

.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1E90FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #999;
}

// 通用flex样式
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.justify-between {
  justify-content: space-between;
}

.news-list {
  padding: 0 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.box_2 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12px;
  width: 702rpx;
  height: 236rpx;
  margin: 24rpx 0 0 0;

  .group_1 {
    width: 368rpx;
    height: 164rpx;
    margin: 16rpx 0 24rpx 30rpx;

    .text_2 {
      width: 368rpx;
      height: 112rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      color: rgba(26, 32, 44, 1);
      font-size: 32rpx;
      font-family: PingFang SC-Regular;
      font-weight: normal;
      text-align: left;
      line-height: 56rpx;
    }

    .section_2 {
      width: 362rpx;
      height: 36rpx;
      margin: 16rpx 0 0 2rpx;

      .box_3 {
        background-color: rgba(249, 249, 249, 1);
        height: 36rpx;
        width: 96rpx;

        .text-wrapper_1 {
          background-color: rgba(68, 119, 221, 0.1);
          border-radius: 2px;
          height: 36rpx;
          width: 96rpx;

          .text_3 {
            width: 80rpx;
            height: 28rpx;
            overflow-wrap: break-word;
            color: rgba(31, 115, 255, 1);
            font-size: 20rpx;
            font-family: PingFang SC-Regular;
            font-weight: normal;
            text-align: left;
            white-space: nowrap;
            line-height: 28rpx;
            margin: 4rpx 0 0 6rpx;
          }
        }
      }

      .thumbnail_1 {
        width: 24rpx;
        height: 24rpx;
        margin: 6rpx 0 0 44rpx;
      }

      .text_4 {
        width: 88rpx;
        height: 28rpx;
        overflow-wrap: break-word;
        color: rgba(147, 152, 160, 1);
        font-size: 20rpx;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 28rpx;
        margin: 4rpx 0 0 8rpx;
      }

      .thumbnail_2 {
        width: 24rpx;
        height: 24rpx;
        margin: 6rpx 0 0 26rpx;
      }

      .text_5 {
        width: 46rpx;
        height: 28rpx;
        overflow-wrap: break-word;
        color: rgba(147, 152, 160, 1);
        font-size: 20rpx;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 28rpx;
        margin: 4rpx 0 0 6rpx;
      }
    }
  }

  .group_2 {
    border-radius: 8px;
    width: 250rpx;
    height: 188rpx;
    margin: 16rpx 24rpx 24rpx 30rpx;
    overflow: hidden;
  }
}

.news-cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-cover-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: white;
  border-radius: 8px;
}

.news-cover-policy {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.news-cover-innovation {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.news-cover-risk {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.news-cover-training {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.news-cover-environment {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.news-cover-report {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.news-cover-default {
  background: linear-gradient(135deg, #ddd6fe, #e879f9);
  color: #8b5cf6;
}



.load-more {
  text-align: center;
  padding: 40rpx 0;
}

.load-more-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  background: #f8f8f8;
  border-radius: 24rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;

  &:active {
    background: #e8e8e8;
    transform: scale(0.98);
  }
}

.load-more-text {
  font-size: 28rpx;
  color: #666;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 20rpx 0;
}

.loading-spinner-small {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #1E90FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-more-text {
  font-size: 26rpx;
  color: #999;
}

.no-more {
  padding: 20rpx 0;
}

.no-more-text {
  font-size: 26rpx;
  color: #ccc;
}
</style> 