<template>
  <view class="faq-list-page">

    <!-- 问题列表 -->
    <view class="faq-content">
      <view v-if="loading" class="loading-section">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载问题...</text>
      </view>
      <view v-else-if="faqList.length === 0" class="empty-section">
        <text class="empty-icon">🤔</text>
        <text class="empty-title">暂无相关问题</text>
        <text class="empty-subtitle">稍后再来看看</text>
      </view>
      <view v-else class="faq-list">
        <view class="group_3 flex-col" v-for="faq in faqList" :key="faq.id" @click="toFAQDetail(faq.id)">
          <view class="block_1 flex-row justify-between">
            <view class="text-wrapper_1 flex-col">
              <text class="text_2">问</text>
            </view>
            <text class="text_3">{{ faq.question }}</text>
          </view>
          <view class="block_2 flex-row justify-between">
            <view class="image-text_1 flex-row justify-between">
              <image
                class="thumbnail_1"
                referrerpolicy="no-referrer"
                src="/static/images/faq-view-icon.png"
              />
              <text class="text-group_1">{{ formatViewCount(faq.view_count) }}</text>
            </view>
            <view class="image-text_2 flex-row justify-between">
              <image
                class="thumbnail_2"
                referrerpolicy="no-referrer"
                src="/static/images/faq-like-icon.png"
              />
              <text class="text-group_2">{{ faq.like_count || 0 }}</text>
            </view>
            <view class="image-text_3 flex-row justify-between">
              <image
                class="thumbnail_3"
                referrerpolicy="no-referrer"
                src="/static/images/faq-collect-icon.png"
              />
              <text class="text-group_3">{{ faq.collect_count || 0 }}</text>
            </view>
          </view>
        </view>
      </view>
      <view v-if="hasMore && !loading" class="load-more">
        <text class="load-more-btn" @click="loadMore">加载更多</text>
      </view>
    </view>
  </view>
</template>

<script>
import { api } from '@/utils/api.js'

export default {
  data() {
    return {
      faqList: [],
      loading: true,
      page: 1,
      perPage: 20,
      hasMore: true
    }
  },
  onLoad() {
    this.loadFAQList()
  },
  onPullDownRefresh() {
    this.refreshList()
  },
  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore()
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    async loadFAQList() {
      try {
        this.loading = true
        const params = {
          page: this.page,
          per_page: this.perPage,
          sort: 'view_count'
        }
        const res = await api.getFaqs(params)
        if (res.success && res.data) {
          const faqs = res.data.items || []
          if (this.page === 1) {
            this.faqList = faqs
          } else {
            this.faqList = [...this.faqList, ...faqs]
          }
          this.hasMore = res.data.has_next || false
        } else {
          this.faqList = []
          this.hasMore = false
        }
      } catch (error) {
        uni.showToast({ title: '加载失败', icon: 'error' })
      } finally {
        this.loading = false
        uni.stopPullDownRefresh()
      }
    },
    refreshList() {
      this.page = 1
      this.hasMore = true
      this.loadFAQList()
    },
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.page++
        this.loadFAQList()
      }
    },
    toFAQDetail(id) {
      uni.navigateTo({ url: `/pages/faq/detail?id=${id}` })
    },
    getAnswerPreview(answer) {
      if (!answer) return ''
      const plainText = answer.replace(/<[^>]*>/g, '')
      return plainText.length > 100 ? plainText.substring(0, 100) + '...' : plainText
    },
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      const now = new Date()
      const diffTime = Math.abs(now - date)
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      if (diffDays === 1) return '昨天'
      if (diffDays <= 7) return `${diffDays}天前`
      return date.toLocaleDateString('zh-CN')
    },
    formatViewCount(count) {
      if (!count) return '0'
      if (count >= 10000) return (count / 10000).toFixed(1) + 'w'
      if (count >= 1000) return (count / 1000).toFixed(1) + 'k'
      return count.toString()
    }
  }
}
</script>

<style scoped>
.faq-list-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.header-left {
  width: 80rpx;
  display: flex;
  align-items: center;
}

.back-icon {
  font-size: 48rpx;
  color: #333;
  font-weight: bold;
}

.header-title {
  flex: 1;
  text-align: center;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
}

.title-subtitle {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-top: 4rpx;
}

.header-right {
  width: 80rpx;
  display: flex;
  justify-content: flex-end;
}

.refresh-btn {
  font-size: 36rpx;
  color: #1E90FF;
}

.faq-content {
  padding: 20rpx 0;
}

.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1E90FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.empty-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #999;
}

// 通用flex样式
.flex-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.justify-between {
  justify-content: space-between;
}

.faq-list {
  padding: 0 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.group_3 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 12px;
  width: 702rpx;
  height: 180rpx;
  margin: 24rpx 0 0 0;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  }

  .block_1 {
    width: 542rpx;
    height: 56rpx;
    margin: 32rpx 0 0 32rpx;

    .text-wrapper_1 {
      background-color: rgba(31, 115, 255, 1);
      height: 48rpx;
      margin-top: 4rpx;
      width: 48rpx;
      border-radius: 4rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .text_2 {
        width: 24rpx;
        height: 48rpx;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 24rpx;
        font-family: PingFang SC-Semibold;
        font-weight: 600;
        text-align: center;
        line-height: 48rpx;
      }
    }

    .text_3 {
      width: 480rpx;
      height: 56rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      color: rgba(26, 32, 44, 1);
      font-size: 32rpx;
      font-family: PingFang SC-Semibold;
      font-weight: 600;
      text-align: left;
      line-height: 28rpx;
      margin-left: 14rpx;
    }
  }

  .block_2 {
    width: 362rpx;
    height: 32rpx;
    margin: 24rpx 0 36rpx 32rpx;

    .image-text_1 {
      width: 74rpx;
      height: 32rpx;

      .thumbnail_1 {
        width: 32rpx;
        height: 32rpx;
      }

      .text-group_1 {
        width: 40rpx;
        height: 32rpx;
        overflow-wrap: break-word;
        color: rgba(144, 146, 149, 1);
        font-size: 24rpx;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 32rpx;
        margin-left: 2rpx;
      }
    }

    .image-text_2 {
      width: 74rpx;
      height: 32rpx;
      margin-left: 70rpx;

      .thumbnail_2 {
        width: 32rpx;
        height: 32rpx;
      }

      .text-group_2 {
        width: 40rpx;
        height: 32rpx;
        overflow-wrap: break-word;
        color: rgba(144, 146, 149, 1);
        font-size: 24rpx;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 32rpx;
        margin-left: 2rpx;
      }
    }

    .image-text_3 {
      width: 74rpx;
      height: 32rpx;
      margin-left: 70rpx;

      .thumbnail_3 {
        width: 32rpx;
        height: 32rpx;
      }

      .text-group_3 {
        width: 40rpx;
        height: 32rpx;
        overflow-wrap: break-word;
        color: rgba(144, 146, 149, 1);
        font-size: 24rpx;
        font-family: PingFang SC-Regular;
        font-weight: normal;
        text-align: left;
        white-space: nowrap;
        line-height: 32rpx;
        margin-left: 2rpx;
      }
    }
  }
}

.load-more {
  text-align: center;
  padding: 40rpx 0;
}

.load-more-btn {
  display: inline-block;
  padding: 20rpx 40rpx;
  background: #f8f8f8;
  border-radius: 24rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.load-more-btn:active {
  background: #e8e8e8;
}
</style> 