{"version": 3, "sources": ["uni-app:///main.js", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/profile/index.vue?ac9c", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/profile/index.vue?cd48", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/profile/index.vue?c816", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/profile/index.vue?8d91", "uni-app:///pages/profile/index.vue", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/profile/index.vue?e98a", "webpack:///F:/商业合作/重庆跨境系统/跨境融资/kjrz-wx/frontend/kjrz_front/pages/profile/index.vue?eea4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "CustomTabBar", "data", "isLoggedIn", "userInfo", "nickname", "avatar", "userStats", "collectionCount", "inquiryCount", "viewCount", "showFeedbackModal", "feedbackContent", "onLoad", "onShow", "methods", "checkLoginStatus", "handleLogin", "uni", "desc", "success", "provider", "params", "code", "title", "api", "res", "finalUserInfo", "icon", "fail", "loadUserInfo", "loadUserStats", "user", "console", "mask", "Promise", "per_page", "action", "collectionRes", "inquiryRes", "viewRes", "toCollectionPage", "url", "toInquiryPage", "toHistoryPage", "promptLogin", "content", "confirmText", "cancelText", "toFeedbackPage", "openService", "phoneNumber", "clearCache", "aboutApp", "showCancel", "submitFeedback"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACyN;AACzN,gBAAgB,uNAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5BA;AAAA;AAAA;AAAA;AAAg3B,CAAgB,i0BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACmKp4B;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAGA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EAAA,CACA;EACAC;IACA;MACA;IACA;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;UAAAX;UAAAC;QAAA;QACA;UAAAE;UAAAC;UAAAC;QAAA;MACA;IACA;IAEAO;MAAA;MACAC;QACAC;QACAC;UACA;YACAf;YACAC;UACA;UACA;;UAEAY;YACAG;YACAD;cAAA;gBAAA;gBAAA;kBAAA;oBAAA;sBAAA;wBACAE;0BACAC;0BACAlB;0BACAC;wBACA;wBAAA;wBAEAY;0BAAAM;wBAAA;wBAAA;wBAAA,OACAC;sBAAA;wBAAAC;wBAAA,MACAA;0BAAA;0BAAA;wBAAA;wBACAR;;wBAEA;wBACAS;wBAEAT;wBACA;wBACA;wBACA;wBACAA;wBACAA;0BAAAM;0BAAAI;wBAAA;wBAAA;wBAAA;sBAAA;wBAAA,MAEA;sBAAA;wBAAA;wBAAA;sBAAA;wBAAA;wBAAA;wBAGAV;wBACAA;0BACAM;0BACAI;wBACA;sBAAA;sBAAA;wBAAA;oBAAA;kBAAA;gBAAA;cAAA,CAEA;cAAA;gBAAA;cAAA;cAAA;YAAA;YACAC;cACAX;gBAAAM;gBAAAI;cAAA;YACA;UACA;QACA;QACAC;UACAX;YAAAM;YAAAI;UAAA;QACA;MACA;IACA;IAEAE;MACA;MACA;QACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEAC;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAC;gBAAA;cAAA;gBAIAf;kBAAAM;kBAAAU;gBAAA;gBAAA;gBAAA;gBAAA,OAEAC,aACAV;kBAAAW;gBAAA,IACAX;kBAAAW;gBAAA,IACAX;kBAAAY;kBAAAD;gBAAA,GACA;cAAA;gBAAA;gBAAA;gBAJAE;gBAAAC;gBAAAC;gBAMA;kBACAhC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAuB;gBACAf;kBACAM;kBACAI;gBACA;gBACA;gBACA;kBACApB;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEAQ;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAuB;MACA;QACA;QACA;MACA;MACAvB;QACAwB;MACA;IACA;IAEAC;MACA;QACA;QACA;MACA;MACAzB;QACAwB;MACA;IACA;IAEAE;MACA;QACA;QACA;MACA;MACA1B;QACAwB;MACA;IACA;IAEAG;MACA3B;QACAM;QACAsB;QACAC;QACAC;QACA5B;UACA;YACA;YACA;UAAA;QAEA;MACA;IACA;IAEA6B;MACA;IACA;IAEAC;MACA;QACA;UACAhC;YACAiC;UACA;UACA;QACA;UACAjC;YACAwB;UACA;UACA;QACA;UACAxB;YACAwB;UACA;UACA;MAAA;IAEA;IAEAU;MAAA;MACAlC;QACAM;QACAsB;QACA1B;UACA;YACA;YACAF;YACA;YACA;YACAA;cACAM;cACAI;YACA;UACA;QACA;MACA;IACA;IAEAyB;MACAnC;QACAM;QACAsB;QACAQ;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACArC;kBACAM;kBACAI;gBACA;gBAAA;cAAA;gBAIA;kBACAV;oBAAAM;kBAAA;;kBAEA;kBACA;;kBAEAN;kBACAA;oBACAM;oBACAI;kBACA;kBAEA;kBACA;gBAEA;kBACAV;kBACAe;kBACAf;oBACAM;oBACAI;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1bA;AAAA;AAAA;AAAA;AAA2oD,CAAgB,g/CAAG,EAAC,C;;;;;;;;;;;ACA/pD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/profile/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/profile/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=14bc1b43&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=14bc1b43&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"14bc1b43\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/profile/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=14bc1b43&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.showFeedbackModal ? _vm.feedbackContent.length : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showFeedbackModal = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.showFeedbackModal = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.showFeedbackModal = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"profile-page\">\n    <!-- 用户信息卡片 -->\n    <view v-if=\"isLoggedIn\" class=\"user-card cq-decoration\">\n      <view class=\"user-avatar-section\">\n        <image :src=\"userInfo.avatar || '/static/images/default-avatar.png'\" class=\"user-avatar\"></image>\n        <view class=\"user-info\">\n          <text class=\"user-name\">{{ userInfo.nickname || '微信用户' }}</text>\n          <text class=\"user-desc\">渝汇万家服务用户</text>\n        </view>\n      </view>\n      <view class=\"user-stats\">\n        <view class=\"stat-item\">\n          <text class=\"stat-number\">{{ userStats.collectionCount || 0 }}</text>\n          <text class=\"stat-label\">收藏</text>\n        </view>\n        <view class=\"stat-item\">\n          <text class=\"stat-number\">{{ userStats.inquiryCount || 0 }}</text>\n          <text class=\"stat-label\">咨询</text>\n        </view>\n        <view class=\"stat-item\">\n          <text class=\"stat-number\">{{ userStats.viewCount || 0 }}</text>\n          <text class=\"stat-label\">浏览</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 登录提示卡片 -->\n    <view v-else class=\"login-prompt-card cq-decoration\">\n      <view class=\"prompt-info\">\n        <text class=\"prompt-title\">登录体验更多服务</text>\n        <text class=\"prompt-desc\">收藏、咨询、浏览记录一目了然</text>\n      </view>\n      <button class=\"login-btn\" @click=\"handleLogin\">\n        <text class=\"login-btn-text\">微信一键登录</text>\n      </button>\n    </view>\n\n    <!-- 功能菜单 -->\n    <view class=\"menu-section section\">\n      <view class=\"menu-list\">\n        <view class=\"menu-item\" @click=\"toCollectionPage\">\n          <view class=\"menu-icon\">⭐</view>\n          <text class=\"menu-title\">我的收藏</text>\n          <view class=\"menu-extra\">\n            <text class=\"menu-count\">{{ userStats.collectionCount || 0 }}</text>\n            <text class=\"menu-arrow\">></text>\n          </view>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"toInquiryPage\">\n          <view class=\"menu-icon\">💬</view>\n          <text class=\"menu-title\">我的咨询</text>\n          <view class=\"menu-extra\">\n            <text class=\"menu-count\">{{ userStats.inquiryCount || 0 }}</text>\n            <text class=\"menu-arrow\">></text>\n          </view>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"toHistoryPage\">\n          <view class=\"menu-icon\">📖</view>\n          <text class=\"menu-title\">浏览历史</text>\n          <view class=\"menu-extra\">\n            <text class=\"menu-arrow\">></text>\n          </view>\n        </view>\n        \n        <!-- <view class=\"menu-item\" @click=\"toFeedbackPage\">\n          <view class=\"menu-icon\">📝</view>\n          <text class=\"menu-title\">意见反馈</text>\n          <view class=\"menu-extra\">\n            <text class=\"menu-arrow\">></text>\n          </view>\n        </view> -->\n      </view>\n    </view>\n\n    <!-- 联系我们 -->\n    <view class=\"service-section section cq-decoration\">\n      <view class=\"section-title\">联系我们</view>\n      <view class=\"service-list\">\n        <view class=\"service-item\" @click=\"openService('hotline')\">\n          <view class=\"service-icon\">📞</view>\n          <view class=\"service-info\">\n            <text class=\"service-title\">外汇局咨询热线</text>\n            <text class=\"service-desc\">023-67677161</text>\n          </view>\n        </view>\n        \n        <view class=\"service-item\" @click=\"openService('location')\">\n          <view class=\"service-icon\">📍</view>\n          <view class=\"service-info\">\n            <text class=\"service-title\">银行网点一览</text>\n            <text class=\"service-desc\">就近银行机构查询</text>\n          </view>\n        </view>\n        \n        <!-- <view class=\"service-item\" @click=\"openService('policy')\">\n          <view class=\"service-icon\">📋</view>\n          <view class=\"service-info\">\n            <text class=\"service-title\">最新政策通知</text>\n            <text class=\"service-desc\">实时政策更新提醒</text>\n          </view>\n        </view> -->\n      </view>\n    </view>\n\n    <!-- 设置选项 -->\n    <view class=\"settings-section section\">\n      <view class=\"menu-list\">\n        <view class=\"menu-item\" @click=\"clearCache\">\n          <view class=\"menu-icon\">🗑️</view>\n          <text class=\"menu-title\">清除缓存</text>\n          <view class=\"menu-extra\">\n            <text class=\"menu-arrow\">></text>\n          </view>\n        </view>\n        \n        <view class=\"menu-item\" @click=\"aboutApp\">\n          <view class=\"menu-icon\">ℹ️</view>\n          <text class=\"menu-title\">关于我们</text>\n          <view class=\"menu-extra\">\n            <text class=\"menu-arrow\">></text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 版本信息 -->\n    <view class=\"version-info\">\n      <text class=\"version-text\">渝汇万家服务</text>\n      <text class=\"copyright-text\">© 2025 重庆市商务委员会</text>\n    </view>\n\n    <!-- 快速反馈弹窗 -->\n    <view class=\"modal-overlay\" v-if=\"showFeedbackModal\" @click=\"showFeedbackModal = false\">\n              <view class=\"feedback-modal\" catchtap=\"true\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">意见反馈</text>\n          <text class=\"modal-close\" @click=\"showFeedbackModal = false\">×</text>\n        </view>\n        <view class=\"modal-content\">\n          <textarea \n            class=\"feedback-textarea\" \n            v-model=\"feedbackContent\" \n            placeholder=\"请描述您遇到的问题或建议...\"\n            maxlength=\"500\"\n          ></textarea>\n          <view class=\"char-count\">{{ feedbackContent.length }}/500</view>\n        </view>\n        <view class=\"modal-footer\">\n          <view class=\"modal-btn cancel-btn\" @click=\"showFeedbackModal = false\">取消</view>\n          <view class=\"modal-btn submit-btn\" @click=\"submitFeedback\">提交</view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 自定义TabBar -->\n    <custom-tab-bar />\n  </view>\n</template>\n\n<script>\nimport { api } from '@/utils/api'\nimport CustomTabBar from '@/custom-tab-bar/index.vue'\n\nexport default {\n  components: {\n    CustomTabBar\n  },\n  data() {\n    return {\n      isLoggedIn: false,\n      userInfo: {\n        nickname: '',\n        avatar: ''\n      },\n      userStats: {\n        collectionCount: 0,\n        inquiryCount: 0,\n        viewCount: 0\n      },\n      showFeedbackModal: false,\n      feedbackContent: ''\n    }\n  },\n  onLoad() {\n    // this.loadUserInfo()\n    // this.loadUserStats()\n  },\n  onShow() {\n    if (typeof this.$root.$mp.page.getTabBar === 'function' && this.$root.$mp.page.getTabBar()) {\n      this.$root.$mp.page.getTabBar().$vm.updateSelected(3)\n    }\n    this.checkLoginStatus();\n  },\n  methods: {\n    checkLoginStatus() {\n      const token = uni.getStorageSync('token');\n      if (token) {\n        this.isLoggedIn = true;\n        this.loadUserInfo();\n        this.loadUserStats();\n      } else {\n        this.isLoggedIn = false;\n        // 清空用户信息和统计数据\n        this.userInfo = { nickname: '微信用户', avatar: '/static/images/default-avatar.png' };\n        this.userStats = { collectionCount: 0, inquiryCount: 0, viewCount: 0 };\n      }\n    },\n\n    handleLogin() {\n      uni.getUserProfile({\n        desc: '用于完善会员资料',\n        success: (userProfileRes) => {\n          const userProfile = {\n            nickname: userProfileRes.userInfo.nickName,\n            avatar: userProfileRes.userInfo.avatarUrl,\n          };\n          this.userInfo = userProfile; // 立即更新UI\n\n          uni.login({\n            provider: 'weixin',\n            success: async (loginRes) => {\n              const params = {\n                code: loginRes.code,\n                nickname: userProfile.nickname,\n                avatar: userProfile.avatar,\n              };\n              try {\n                uni.showLoading({ title: '登录中...' });\n                const res = await api.wechatLogin(params);\n                if (res.code === 200 && res.data.token) {\n                  uni.setStorageSync('token', res.data.user.id);\n                  \n                  // 合并后端返回的用户信息（如ID）和从微信获取的最新信息\n                  const finalUserInfo = { ...res.data.user, ...userProfile };\n\n                  uni.setStorageSync('userInfo', finalUserInfo);\n                  this.isLoggedIn = true;\n                  this.userInfo = finalUserInfo; // 再次更新，确保数据完整\n                  this.loadUserStats();\n                  uni.hideLoading();\n                  uni.showToast({ title: '登录成功', icon: 'success' });\n                } else {\n                  throw new Error('登录失败，请重试');\n                }\n              } catch (error) {\n                uni.hideLoading();\n                uni.showToast({\n                  title: error.message || '登录时发生错误',\n                  icon: 'none',\n                });\n              }\n            },\n            fail: (err) => {\n                uni.showToast({ title: '微信登录授权失败', icon: 'none' });\n            }\n          });\n        },\n        fail: (err) => {\n          uni.showToast({ title: '您取消了授权', icon: 'none' });\n        },\n      });\n    },\n\n    loadUserInfo() {\n      const userInfo = uni.getStorageSync('userInfo');\n      if (userInfo) {\n        this.userInfo = userInfo;\n      }\n    },\n\n    async loadUserStats() {\n      if (!this.isLoggedIn) return;\n      \n      const user = uni.getStorageSync('userInfo');\n      if (!user || !user.id) {\n        console.error(\"无法获取用户信息或用户ID\");\n        return;\n      }\n\n      uni.showLoading({ title: '加载中...', mask: true });\n      try {\n        const [collectionRes, inquiryRes, viewRes] = await Promise.all([\n          api.getMyCollections({ per_page: 1 }),\n          api.getMyInquiries(user.id, { per_page: 1 }),\n          api.getMyInteractions({ action: 'view', per_page: 1 })\n        ]);\n\n        this.userStats = {\n          collectionCount: collectionRes.data.total || 0,\n          inquiryCount: inquiryRes.data.total || 0,\n          viewCount: viewRes.data.total || 0,\n        };\n      } catch (error) {\n        console.error('加载用户统计失败:', error);\n        uni.showToast({\n          title: '统计数据加载失败',\n          icon: 'none'\n        });\n        // 保留旧数据或清零\n        this.userStats = {\n          collectionCount: 'N/A',\n          inquiryCount: 'N/A',\n          viewCount: 'N/A'\n        };\n      } finally {\n        uni.hideLoading();\n      }\n    },\n\n    toCollectionPage() {\n      if (!this.isLoggedIn) {\n        this.promptLogin('查看收藏');\n        return;\n      }\n      uni.navigateTo({\n        url: '/pages/profile/collection'\n      })\n    },\n\n    toInquiryPage() {\n      if (!this.isLoggedIn) {\n        this.promptLogin('查看咨询');\n        return;\n      }\n      uni.navigateTo({\n        url: '/pages/profile/inquiry'\n      })\n    },\n\n    toHistoryPage() {\n      if (!this.isLoggedIn) {\n        this.promptLogin('查看浏览历史');\n        return;\n      }\n      uni.navigateTo({\n        url: '/pages/profile/history'\n      })\n    },\n\n    promptLogin(action) {\n      uni.showModal({\n        title: '请先登录',\n        content: `登录后才能${action}哦`,\n        confirmText: '去登录',\n        cancelText: '取消',\n        success: (res) => {\n          if (res.confirm) {\n            // 用户点击确认，可以在此处理登录逻辑，或者停留在当前页让用户点击登录按钮\n            // 当前逻辑是让用户停留在个人中心页手动登录\n          }\n        }\n      });\n    },\n\n    toFeedbackPage() {\n      this.showFeedbackModal = true\n    },\n\n    openService(type) {\n      switch (type) {\n        case 'hotline':\n          uni.makePhoneCall({\n            phoneNumber: '023-********'\n          })\n          break\n        case 'location':\n          uni.navigateTo({\n            url: '/pages/bank/list'\n          })\n          break\n        case 'policy':\n          uni.switchTab({\n            url: '/pages/policy/index'\n          })\n          break\n      }\n    },\n\n    clearCache() {\n      uni.showModal({\n        title: '清除缓存',\n        content: '确认清除所有缓存数据吗？',\n        success: (res) => {\n          if (res.confirm) {\n            // 清除缓存逻辑\n            uni.clearStorageSync()\n            this.isLoggedIn = false;\n            this.checkLoginStatus(); // 重新检查状态并更新UI\n            uni.showToast({\n              title: '缓存已清除',\n              icon: 'success'\n            })\n          }\n        }\n      })\n    },\n\n    aboutApp() {\n      uni.showModal({\n        title: '关于我们',\n        content: '重庆跨境融资服务小程序\\n\\n为企业提供专业的跨境融资政策咨询服务，助力重庆开放型经济发展。\\n\\n联系我们：023-********',\n        showCancel: false\n      })\n    },\n\n    async submitFeedback() {\n      if (!this.feedbackContent.trim()) {\n        uni.showToast({\n          title: '请输入反馈内容',\n          icon: 'none'\n        })\n        return\n      }\n\n      try {\n        uni.showLoading({ title: '提交中...' })\n        \n        // 提交反馈接口\n        // await api.submitFeedback({ content: this.feedbackContent })\n        \n        uni.hideLoading()\n        uni.showToast({\n          title: '提交成功',\n          icon: 'success'\n        })\n        \n        this.showFeedbackModal = false\n        this.feedbackContent = ''\n        \n      } catch (error) {\n        uni.hideLoading()\n        console.error('提交反馈失败:', error)\n        uni.showToast({\n          title: '提交失败，请重试',\n          icon: 'none'\n        })\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.profile-page {\n  min-height: 100vh;\n  background: linear-gradient(180deg, #fef5f7 0%, #f8f9fa 40%, #f1f3f5 100%);\n  padding-bottom: 140rpx; /* 为自定义tabBar留出空间 */\n  position: relative;\n}\n\n/* 顶部重庆山城风格背景 */\n.profile-page::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 300rpx;\n  background: linear-gradient(180deg, \n    #1E90FF 0%, \n    #4A90E2 25%, \n    #6BA3E8 50%, \n    rgba(107, 163, 232, 0.7) 70%, \n    rgba(138, 180, 240, 0.4) 85%, \n    rgba(169, 197, 248, 0.2) 95%, \n    transparent 100%\n  );\n  z-index: 1;\n}\n\n/* 重庆山城剪影装饰 */\n.profile-page::after {\n  content: '';\n  position: absolute;\n  top: 180rpx;\n  left: 0;\n  right: 0;\n  height: 100rpx;\n  background: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 100'%3E%3Cpath d='M0,80 L50,75 L100,70 L150,65 L200,60 L250,65 L300,55 L350,60 L400,50 L450,55 L500,45 L550,50 L600,40 L650,45 L700,35 L750,40 L800,30 L850,35 L900,25 L950,30 L1000,20 L1050,25 L1100,15 L1150,20 L1200,10 L1200,100 L0,100 Z' fill='%23ffffff' fill-opacity='0.1'/%3E%3C/svg%3E\") repeat-x;\n  background-size: 1200rpx 100rpx;\n  z-index: 2;\n  opacity: 0.8;\n}\n\n.user-card {\n  margin: 0 30rpx 30rpx;\n  margin-top: 0;\n  padding: 40rpx 30rpx;\n  background: linear-gradient(135deg, #ffffff, #f8f9ff);\n  border-radius: 20rpx;\n  color: #333;\n  position: relative;\n  z-index: 3;\n  box-shadow: 0 8rpx 32rpx rgba(30, 144, 255, 0.15);\n  border: 1rpx solid rgba(30, 144, 255, 0.1);\n}\n\n.user-avatar-section {\n  display: flex;\n  align-items: center;\n  margin-bottom: 40rpx;\n}\n\n.user-avatar {\n  width: 120rpx;\n  height: 120rpx;\n  border-radius: 50%;\n  border: 4rpx solid rgba(30, 144, 255, 0.2);\n  margin-right: 30rpx;\n}\n\n.user-info {\n  flex: 1;\n}\n\n.user-name {\n  display: block;\n  font-size: 36rpx;\n  font-weight: bold;\n  margin-bottom: 12rpx;\n  color: #333;\n}\n\n.user-desc {\n  font-size: 26rpx;\n  color: #666;\n}\n\n.user-stats {\n  display: flex;\n  justify-content: space-around;\n  background: rgba(30, 144, 255, 0.05);\n  border-radius: 16rpx;\n  padding: 30rpx 0;\n  border: 1rpx solid rgba(30, 144, 255, 0.1);\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.stat-number {\n  font-size: 40rpx;\n  font-weight: bold;\n  margin-bottom: 8rpx;\n  color: #1E90FF;\n}\n\n.stat-label {\n  font-size: 24rpx;\n  color: #666;\n}\n\n.section {\n  background: white;\n  border-radius: 16rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);\n  overflow: hidden;\n  position: relative;\n  z-index: 3;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  padding: 30rpx 30rpx 20rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.menu-section {\n  margin: 0 30rpx 30rpx;\n}\n\n.menu-list {\n  padding: 0 30rpx;\n}\n\n.menu-item {\n  display: flex;\n  align-items: center;\n  padding: 32rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.menu-item:last-child {\n  border-bottom: none;\n}\n\n.menu-icon {\n  font-size: 40rpx;\n  margin-right: 24rpx;\n  width: 60rpx;\n  text-align: center;\n}\n\n.menu-title {\n  flex: 1;\n  font-size: 30rpx;\n  color: var(--text-color);\n}\n\n.menu-extra {\n  display: flex;\n  align-items: center;\n  gap: 16rpx;\n}\n\n.menu-count {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.menu-arrow {\n  font-size: 28rpx;\n  color: #999;\n}\n\n.service-section {\n  margin: 0 30rpx 30rpx;\n}\n\n.service-list {\n  padding: 30rpx;\n}\n\n.service-item {\n  display: flex;\n  align-items: center;\n  padding: 24rpx 0;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.service-item:last-child {\n  border-bottom: none;\n}\n\n.service-icon {\n  font-size: 40rpx;\n  margin-right: 24rpx;\n  width: 60rpx;\n  text-align: center;\n}\n\n.service-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.service-title {\n  font-size: 30rpx;\n  color: var(--text-color);\n}\n\n.service-desc {\n  font-size: 24rpx;\n  color: #999;\n}\n\n.settings-section {\n  margin: 0 30rpx 30rpx;\n}\n\n.version-info {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 60rpx 30rpx;\n  gap: 16rpx;\n}\n\n.version-text {\n  font-size: 26rpx;\n  color: #999;\n}\n\n.copyright-text {\n  font-size: 24rpx;\n  color: #ccc;\n}\n\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.feedback-modal {\n  width: 600rpx;\n  background: white;\n  border-radius: 16rpx;\n  overflow: hidden;\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 30rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n}\n\n.modal-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: var(--text-color);\n}\n\n.modal-close {\n  font-size: 40rpx;\n  color: #999;\n}\n\n.modal-content {\n  padding: 30rpx;\n}\n\n.feedback-textarea {\n  width: 100%;\n  height: 200rpx;\n  padding: 20rpx;\n  background: #f8f8f8;\n  border-radius: 12rpx;\n  font-size: 28rpx;\n  resize: none;\n  border: none;\n}\n\n.char-count {\n  text-align: right;\n  font-size: 24rpx;\n  color: #999;\n  margin-top: 12rpx;\n}\n\n.modal-footer {\n  display: flex;\n  border-top: 1rpx solid #f0f0f0;\n}\n\n.modal-btn {\n  flex: 1;\n  padding: 30rpx;\n  text-align: center;\n  font-size: 30rpx;\n}\n\n.cancel-btn {\n  color: #999;\n  border-right: 1rpx solid #f0f0f0;\n}\n\n.submit-btn {\n  color: var(--primary-color);\n  font-weight: bold;\n}\n\n.login-prompt-card {\n  margin: 0 30rpx 30rpx;\n  padding: 60rpx 40rpx;\n  background: linear-gradient(135deg, #1E90FF, #4A90E2);\n  border-radius: 20rpx;\n  color: white;\n  position: relative;\n  z-index: 3;\n  box-shadow: 0 8rpx 32rpx rgba(30, 144, 255, 0.3);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n}\n\n.prompt-info {\n  margin-bottom: 40rpx;\n}\n\n.prompt-title {\n  display: block;\n  font-size: 38rpx;\n  font-weight: bold;\n  margin-bottom: 16rpx;\n  color: white;\n}\n\n.prompt-desc {\n  font-size: 26rpx;\n  color: rgba(255, 255, 255, 0.9);\n}\n\n.login-btn {\n  background-color: #ffffff;\n  color: #1E90FF;\n  border-radius: 50rpx;\n  padding: 24rpx 60rpx;\n  font-size: 30rpx;\n  font-weight: bold;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border: none;\n}\n\n.login-btn-text {\n  margin-left: 10rpx;\n}\n</style> ", "import mod from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=14bc1b43&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=14bc1b43&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751949051466\n      var cssReload = require(\"F:/LeStoreDownload/HBuilderX.4.36.2024112817/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}