<script>
export default {
  onLaunch: function() {
    console.log('App Launch')
  },
  onShow: function() {
    console.log('App Show')
  },
  onHide: function() {
    console.log('App Hide')
  }
}
</script>

<style lang="scss">
/* 引入公共样式 */
@import './static/css/common.scss';

/* 全局样式 */
// 引入SCSS变量以便在全局样式中使用
@import './static/css/variables.scss';

page {
  background-color: $gray-light;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
  font-size: $text-base;
  line-height: 1.6;
}

/* uni-app框架特定样式 - 保留 */
.uni-navigation-bar {
  height: $navbar-height !important;
}

.uni-navigation-bar .uni-navigation-bar-text {
  font-size: $text-xl !important;
  font-weight: 600 !important;
}

.uni-tabbar {
  height: $tabbar-height !important;
  padding-top: $spacing-1 !important;
  padding-bottom: $spacing-1 !important;
}

.uni-tabbar-item {
  font-size: $text-sm !important;
}

.uni-tabbar .uni-tabbar-item-icon {
  width: 48rpx !important;
  height: 48rpx !important;
}

/*
// 注释掉旧的按钮和输入框样式，使用新的SCSS混合器
// 按钮优化 - 已在common.scss中通过混合器实现
button {
  font-size: 32rpx !important;
  padding: 20rpx 40rpx !important;
  border-radius: 12rpx !important;
}

// 输入框优化 - 已在mixins.scss中通过混合器实现
input, textarea {
  font-size: 30rpx !important;
  padding: 20rpx !important;
}
*/

/* 项目特色样式 - 保留并优化 */

/* 矢量图标通用样式 */
.emoji-icon {
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif;
  line-height: 1;
  font-style: normal;
}

/* 手机端图标放大 - 使用SCSS变量 */
.icon-large {
  font-size: $text-3xl !important;
}

.icon-medium {
  font-size: $text-xl !important;
}

.icon-small {
  font-size: $text-base !important;
}

/*
// 注释掉旧的CSS变量和通用样式，已在SCSS变量和工具类中实现
// CSS变量定义 - 已在variables.scss中使用SCSS变量替代
:root {
  --primary-color: #1E7DFA;
  --secondary-color: #319DFD;
  --background-color: #F2F5F7;
  --text-color: #333333;
  --border-color: #E5E5E5;
  --gray-light: #F5F5F5;
  --gray-medium: #999999;
}

// 通用样式 - 已在common.scss中通过工具类实现
.container {
  padding: 0 30rpx;
  background-color: var(--background-color);
}

.section {
  margin-bottom: 30rpx;
  background-color: var(--background-color);
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--primary-color);
  padding: 30rpx;
  border-bottom: 1rpx solid var(--border-color);
}

// Flex布局 - 已在common.scss中通过工具类实现
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 文本省略 - 已在common.scss中通过工具类和混合器实现
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
*/

/* 重庆特色装饰元素 - 保留并优化 */
.cq-decoration {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
      rgba($primary-color, 0.1) 0%,
      rgba($secondary-color, 0.1) 100%
    );
    border-radius: inherit;
    pointer-events: none;
  }
}
</style> 